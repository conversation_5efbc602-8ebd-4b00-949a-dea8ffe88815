"""add language field to videos

Revision ID: 003
Revises: 002
Create Date: 2024-04-23 00:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '003'
down_revision: Union[str, None] = '002'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add language column to videos table
    op.add_column('videos', sa.Column('language', sa.String(), nullable=True))
    op.create_index('ix_videos_language', 'videos', ['language'])


def downgrade() -> None:
    # Drop language column from videos table
    op.drop_index('ix_videos_language', table_name='videos')
    op.drop_column('videos', 'language') 