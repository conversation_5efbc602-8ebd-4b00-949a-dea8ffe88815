"""add job status enum

Revision ID: 002
Revises: 001
Create Date: 2024-04-23 00:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import Enum

# revision identifiers, used by Alembic.
revision: str = '002'
down_revision: Union[str, None] = '001'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

def upgrade() -> None:
    # Create enum type if it doesn't exist
    op.execute("DO $$ BEGIN CREATE TYPE jobstatus AS ENUM ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED'); EXCEPTION WHEN duplicate_object THEN NULL; END $$;")
    
    # Temporarily create a new status column with the enum type
    op.add_column('analysis_jobs', sa.Column('new_status', sa.Enum('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', name='jobstatus'), nullable=True))
    
    # Convert existing status values to uppercase to match enum values
    op.execute("UPDATE analysis_jobs SET new_status = UPPER(status)::jobstatus;")
    
    # Drop the old status column and rename the new one
    op.drop_column('analysis_jobs', 'status')
    op.alter_column('analysis_jobs', 'new_status', new_column_name='status', nullable=False,
                    server_default=sa.text("'PENDING'::jobstatus"))
    
    # Recreate the indexes
    op.create_index('ix_analysis_jobs_status', 'analysis_jobs', ['status'])
    op.create_index('ix_analysis_jobs_video_id_status', 'analysis_jobs', ['video_id', 'status'])

def downgrade() -> None:
    # Drop indexes
    op.drop_index('ix_analysis_jobs_video_id_status', table_name='analysis_jobs')
    op.drop_index('ix_analysis_jobs_status', table_name='analysis_jobs')
    
    # Convert back to string column
    op.alter_column('analysis_jobs', 'status',
                    type_=sa.String(),
                    postgresql_using="status::text",
                    nullable=False,
                    server_default='pending')
    
    # Drop the enum type
    op.execute("DROP TYPE jobstatus;") 