"""initial migration

Revision ID: 001
Revises: 
Create Date: 2024-04-23 00:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '001'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create videos table
    op.create_table(
        'videos',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('title', sa.String(), nullable=False),
        sa.Column('file_path', sa.String(), nullable=False, unique=True),
        sa.Column('duration', sa.Float(), nullable=True),
        sa.Column('width', sa.Integer(), nullable=True),
        sa.Column('height', sa.Integer(), nullable=True),
        sa.Column('tiktok_url', sa.String(), nullable=True),
        sa.Column('tiktok_author', sa.String(), nullable=True),
        sa.Column('download_date', sa.DateTime(), nullable=False),
        sa.Column('transcript', sa.String(), nullable=True),
        sa.Column('main_topic', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_videos_download_date', 'videos', ['download_date'])
    op.create_index('ix_videos_title_tiktok_author', 'videos', ['title', 'tiktok_author'])

    # Create tags table
    op.create_table(
        'tags',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False, unique=True),
        sa.Column('description', sa.String(), nullable=True),
        sa.Column('is_ai_generated', sa.Boolean(), nullable=False, default=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_tags_name', 'tags', ['name'])

    # Create video_tag association table
    op.create_table(
        'video_tag',
        sa.Column('video_id', sa.Integer(), nullable=False),
        sa.Column('tag_id', sa.Integer(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['video_id'], ['videos.id'], ),
        sa.ForeignKeyConstraint(['tag_id'], ['tags.id'], ),
        sa.PrimaryKeyConstraint('video_id', 'tag_id')
    )

    # Create analysis_jobs table
    op.create_table(
        'analysis_jobs',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('video_id', sa.Integer(), nullable=False),
        sa.Column('status', sa.String(), nullable=False, default='pending'),
        sa.Column('error_message', sa.String(), nullable=True),
        sa.Column('frame_labels', sa.String(), nullable=True),
        sa.Column('started_at', sa.DateTime(), nullable=True),
        sa.Column('completed_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(['video_id'], ['videos.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_analysis_jobs_status', 'analysis_jobs', ['status'])
    op.create_index('ix_analysis_jobs_video_id_status', 'analysis_jobs', ['video_id', 'status'])


def downgrade() -> None:
    op.drop_index('ix_analysis_jobs_video_id_status', table_name='analysis_jobs')
    op.drop_index('ix_analysis_jobs_status', table_name='analysis_jobs')
    op.drop_table('analysis_jobs')
    op.drop_table('video_tag')
    op.drop_index('ix_tags_name', table_name='tags')
    op.drop_table('tags')
    op.drop_index('ix_videos_title_tiktok_author', table_name='videos')
    op.drop_index('ix_videos_download_date', table_name='videos')
    op.drop_table('videos')
