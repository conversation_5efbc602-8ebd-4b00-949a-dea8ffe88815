#!/usr/bin/env python3
import os
import sys
import argparse
import logging
from datetime import datetime
from sqlmodel import Session, select
import cv2

# Add the current directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.models.models import Video, AnalysisJob, JobStatus
from src.utils.db import engine
from src.tasks.tasks import analyze_video

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Supported video extensions
SUPPORTED_EXTENSIONS = ['.mp4', '.mov', '.avi', '.mkv', '.webm']


def get_video_metadata(video_path):
    """
    Extract metadata from a video file using OpenCV.
    """
    try:
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            logger.warning(f"Could not open video file: {video_path}")
            return None
        
        # Get video properties
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        duration = frame_count / fps if fps > 0 else 0
        
        cap.release()
        
        return {
            'width': width,
            'height': height,
            'duration': duration
        }
    except Exception as e:
        logger.error(f"Error extracting metadata from {video_path}: {e}")
        return None


def scan_videos_directory(videos_dir, force_reanalysis=False):
    """
    Scan the videos directory for new files and queue them for analysis.
    """
    logger.info(f"Scanning directory: {videos_dir}")
    
    # Check if the directory exists
    if not os.path.isdir(videos_dir):
        logger.error(f"Directory not found: {videos_dir}")
        return
    
    # Get all video files in the directory
    video_files = []
    for root, _, files in os.walk(videos_dir):
        for file in files:
            if any(file.lower().endswith(ext) for ext in SUPPORTED_EXTENSIONS):
                full_path = os.path.join(root, file)
                rel_path = os.path.relpath(full_path, videos_dir)
                video_files.append(rel_path)
    
    logger.info(f"Found {len(video_files)} video files")
    
    # Create a database session
    with Session(engine) as session:
        # Get all existing videos from the database
        existing_videos = session.exec(select(Video)).all()
        existing_paths = {video.file_path for video in existing_videos}
        
        # Find new videos
        new_videos = [path for path in video_files if path not in existing_paths]
        logger.info(f"Found {len(new_videos)} new videos")
        
        # Process new videos
        for video_path in new_videos:
            try:
                # Get the full path to the video file
                full_path = os.path.join(videos_dir, video_path)
                
                # Extract video metadata
                metadata = get_video_metadata(full_path)
                if not metadata:
                    logger.warning(f"Skipping {video_path} due to metadata extraction failure")
                    continue
                
                # Create a new video record
                video = Video(
                    title=os.path.basename(video_path),
                    file_path=video_path,
                    duration=metadata['duration'],
                    width=metadata['width'],
                    height=metadata['height'],
                    download_date=datetime.utcnow()
                )
                
                session.add(video)
                session.commit()
                session.refresh(video)
                
                logger.info(f"Added new video: {video.title} (ID: {video.id})")
                
                # Create an analysis job
                job = AnalysisJob(
                    video_id=video.id,
                    status=JobStatus.PENDING
                )
                
                session.add(job)
                session.commit()
                
                # Queue the video for analysis
                analyze_video.delay(video.id)
                logger.info(f"Queued analysis for video ID: {video.id}")
            
            except Exception as e:
                logger.error(f"Error processing {video_path}: {e}")
        
        # If force_reanalysis is True, queue all videos for analysis
        if force_reanalysis:
            logger.info("Force reanalysis requested, queueing all videos")
            for video in existing_videos:
                try:
                    # Create a new analysis job
                    job = AnalysisJob(
                        video_id=video.id,
                        status=JobStatus.PENDING
                    )
                    
                    session.add(job)
                    session.commit()
                    
                    # Queue the video for analysis
                    analyze_video.delay(video.id)
                    logger.info(f"Queued reanalysis for video ID: {video.id}")
                
                except Exception as e:
                    logger.error(f"Error queueing reanalysis for video ID {video.id}: {e}")


def main():
    parser = argparse.ArgumentParser(description='Scan videos directory and queue analysis tasks')
    parser.add_argument('--videos-dir', type=str, default=os.environ.get('VIDEOS_DIR', '/videos'),
                        help='Directory containing video files')
    parser.add_argument('--force-reanalysis', action='store_true',
                        help='Force reanalysis of all videos, even if already analyzed')
    
    args = parser.parse_args()
    
    scan_videos_directory(args.videos_dir, args.force_reanalysis)
    logger.info("Bootstrap process completed")


if __name__ == '__main__':
    main()
