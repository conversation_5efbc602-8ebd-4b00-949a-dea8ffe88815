import os
from celery import Celery

# Explicitly import tasks to ensure registration
# from src import tasks # Remove this if present

# Get Redis URL from environment variable with a default value
redis_url = os.getenv('REDIS_URL', 'redis://redis:6379/0')

# Initialize Celery app
celery_app = Celery(
    'src.worker',
    broker=redis_url,
    backend=redis_url,
    task_serializer='json',
    result_serializer='json',
    accept_content=['json'],
    include=['src.tasks.tasks'] # Use include with the correct path to the file
)

# Configure Celery
celery_app.conf.update(
    timezone='UTC',
    enable_utc=True,
    # beat_schedule={  <-- Comment out or remove this block
    #     'placeholder-task': {
    #         'task': 'src.tasks.tasks.placeholder_task',
    #         'schedule': 30.0,
    #         'args': (16, 16)
    #     }
    # }
)

# === Explicit Task Registration ===
# # Import tasks directly
# from src.tasks import placeholder_task, analyze_video
# 
# # Manually register tasks with the app instance
# # Note: This might redefine them if discovery also worked, but ensures they exist.
# celery_app.register_task(placeholder_task)
# celery_app.register_task(analyze_video)
# # =================================


# Optional: If you are using Django-Celery-Beat with DatabaseScheduler
# you might need additional setup depending on how you integrate it
# with SQLModel/SQLAlchemy instead of the Django ORM.
# For now, we assume the scheduler specified in docker-compose is sufficient.

if __name__ == '__main__':
    # This allows running the worker directly for testing, e.g., python -m src.worker worker
    celery_app.start() 