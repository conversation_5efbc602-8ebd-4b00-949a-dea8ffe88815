from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File, Form
from sqlmodel import Session, select, or_
from sqlalchemy.orm import selectinload
from datetime import datetime, date
import shutil
import os
from fastapi.responses import StreamingResponse, FileResponse
import ffmpeg

from ..models.models import Video, VideoCreate, VideoRead, VideoUpdate, Tag, AnalysisJob
from ..utils.db import get_session
from ..tasks.tasks import analyze_video

router = APIRouter(tags=["videos"])

# Directory for storing uploaded videos
UPLOAD_DIR = os.getenv("VIDEOS_DIR", "/videos")
os.makedirs(UPLOAD_DIR, exist_ok=True)


@router.get("/", response_model=List[VideoRead])
def get_videos(
    session: Session = Depends(get_session),
    skip: int = 0,
    limit: int = 100,
    tag_ids: Optional[List[int]] = Query(None),
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    search: Optional[str] = None
):
    """
    Get a list of videos with optional filtering by tags, date range, and search term.
    Eagerly loads tags for each video.
    """
    # Start base query with eager loading for tags
    query = select(Video).options(selectinload(Video.tags))
    
    # Filter by tags ONLY if tag_ids are provided
    if tag_ids:
        # Apply join specifically for filtering
        query = query.join(Video.tags).where(Tag.id.in_(tag_ids))
    
    # Filter by date range if provided
    if start_date:
        start_datetime = datetime.combine(start_date, datetime.min.time())
        query = query.where(Video.download_date >= start_datetime)
    
    if end_date:
        end_datetime = datetime.combine(end_date, datetime.max.time())
        query = query.where(Video.download_date <= end_datetime)
    
    # Filter by search term if provided
    if search:
        search_term = f"%{search}%"
        query = query.where(
            or_(
                Video.title.ilike(search_term),
                Video.tiktok_author.ilike(search_term),
                # Ensure relationship loaded models can be searched if needed
                # Assuming transcript and main_topic are direct columns on Video
                Video.transcript.ilike(search_term), 
                Video.main_topic.ilike(search_term) 
            )
        )
    
    # Apply pagination
    query = query.order_by(Video.id).offset(skip).limit(limit) # Added default order_by for consistency
    
    try:
        videos = session.exec(query).unique().all() # Use .unique() after joins
    except Exception as e:
        print(f"Error executing video query: {e}") # Log potential query errors
        raise HTTPException(status_code=500, detail="Error fetching videos")

    return videos


@router.post("/upload", response_model=VideoRead)
async def upload_video(
    *,
    session: Session = Depends(get_session),
    file: UploadFile = File(...),
    title: str = Form(...),
    analyze: bool = Form(True)  # Optional parameter to control if video should be analyzed
):
    """
    Upload a new video file and optionally queue it for analysis.
    """
    # Define file path
    file_path = os.path.join(UPLOAD_DIR, file.filename)
    
    # Check if a video record with this file_path already exists
    existing_video = session.exec(select(Video).where(Video.file_path == file.filename)).first()
    if existing_video:
        # Optionally, raise an error or handle update/overwrite logic here
        # For now, let's raise a conflict error
        raise HTTPException(status_code=409, detail=f"Video with file name {file.filename} already exists.")

    # Delete physical file if it exists (to prevent issues if DB entry was deleted but file remained)
    if os.path.exists(file_path):
        try:
            os.remove(file_path)
            print(f"Removed existing file: {file_path}") # Added for debugging
        except OSError as e:
            print(f"Error removing existing file {file_path}: {e}") # Added for debugging
            # Decide if this should be a fatal error

    # Save the uploaded file
    # file_path = os.path.join(UPLOAD_DIR, file.filename)
    try:
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to save file: {str(e)}")
    
    # Create video record
    db_video = Video(title=title, file_path=file.filename)
    session.add(db_video)
    session.commit()
    session.refresh(db_video)
    
    # Queue for analysis if requested
    print(f"Checking if analysis should be queued for video {db_video.id}. Analyze flag: {analyze}") # Debug log
    if analyze:
        try:
            print(f"Queuing analysis task for video ID: {db_video.id}") # Debug log
            task = analyze_video.delay(db_video.id)
            print(f"Task queued successfully, task ID: {task.id}") # Debug log
        except Exception as e:
            print(f"Error queuing analysis task for video ID {db_video.id}: {str(e)}") # Debug log
            # Decide how to handle queueing errors - maybe raise HTTPException?
            
    else:
        print(f"Analysis not requested for video ID: {db_video.id}") # Debug log

    return db_video


@router.get("/{video_id}", response_model=VideoRead)
def get_video(*, session: Session = Depends(get_session), video_id: int):
    """
    Get a specific video by ID.
    """
    video = session.get(Video, video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    return video


@router.post("/", response_model=VideoRead)
def create_video(*, session: Session = Depends(get_session), video: VideoCreate):
    """
    Create a new video record.
    """
    db_video = Video.from_orm(video)
    session.add(db_video)
    session.commit()
    session.refresh(db_video)
    
    # Queue video for analysis
    analyze_video.delay(db_video.id)
    
    return db_video


@router.patch("/{video_id}", response_model=VideoRead)
def update_video(
    *, session: Session = Depends(get_session), video_id: int, video: VideoUpdate
):
    """
    Update a video record.
    """
    db_video = session.get(Video, video_id)
    if not db_video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    video_data = video.dict(exclude_unset=True)
    for key, value in video_data.items():
        setattr(db_video, key, value)
    
    session.add(db_video)
    session.commit()
    session.refresh(db_video)
    return db_video


@router.delete("/{video_id}")
def delete_video(*, session: Session = Depends(get_session), video_id: int):
    """
    Delete a video record and its association with tags and analysis jobs.
    """
    video = session.exec(select(Video).options(selectinload(Video.tags)).where(Video.id == video_id)).first()
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    # Delete the video file if it exists
    file_path = os.path.join(UPLOAD_DIR, video.file_path)
    if os.path.exists(file_path):
        try:
            os.remove(file_path)
        except Exception as e:
            print(f"Failed to delete file {file_path}: {str(e)}")
    
    # Clear the relationship before deleting the video object
    video.tags.clear()
    session.add(video)
    
    # Delete all analysis jobs for this video
    analysis_jobs = session.exec(select(AnalysisJob).where(AnalysisJob.video_id == video_id)).all()
    for job in analysis_jobs:
        session.delete(job)
    
    # Now delete the video object itself
    session.delete(video)
    try:
        session.commit()
    except Exception as e:
        session.rollback()
        print(f"Database error during video deletion: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database error during deletion: {str(e)}")
    return {"ok": True}


@router.post("/{video_id}/analyze")
def trigger_analysis(*, session: Session = Depends(get_session), video_id: int):
    """
    Trigger a new analysis for a video.
    """
    video = session.get(Video, video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    # Queue video for analysis
    task_id = analyze_video.delay(video_id)
    
    return {"message": "Analysis queued", "task_id": str(task_id)}


# Thumbnail Endpoint
@router.get("/{video_id}/thumbnail", response_class=FileResponse)
async def get_thumbnail(video_id: int, session: Session = Depends(get_session)):
    """
    Serve the thumbnail for the video.
    If the thumbnail does not exist, generate it using ffmpeg.
    """
    video = session.get(Video, video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    video_path = os.path.join(UPLOAD_DIR, video.file_path)
    thumbnail_filename = f"thumbnail_{video.id}.jpg"
    thumbnail_path = os.path.join(UPLOAD_DIR, thumbnail_filename)

    # Check if thumbnail exists
    if not os.path.exists(thumbnail_path):
        if not os.path.exists(video_path):
            raise HTTPException(status_code=404, detail="Video file not found, cannot generate thumbnail")

        try:
            print(f"Generating thumbnail for {video_path} at {thumbnail_path}")
            (
                ffmpeg
                .input(video_path, ss=1) # Seek to 1 second
                .output(thumbnail_path, vframes=1, format='image2', vcodec='mjpeg')
                .overwrite_output()
                .run(capture_stdout=True, capture_stderr=True)
            )
            print(f"Thumbnail generated successfully for video {video.id}")
        except ffmpeg.Error as e:
            print(f"ffmpeg error generating thumbnail: {e.stderr.decode()}")
            raise HTTPException(status_code=500, detail=f"Failed to generate thumbnail: {e.stderr.decode()}")
        except Exception as e:
            print(f"Unexpected error generating thumbnail: {str(e)}")
            raise HTTPException(status_code=500, detail=f"An unexpected error occurred while generating thumbnail: {str(e)}")

    # Check again if thumbnail exists after generation attempt
    if not os.path.exists(thumbnail_path):
        raise HTTPException(status_code=404, detail="Thumbnail not found after generation attempt")

    return FileResponse(thumbnail_path, media_type="image/jpeg")


# Add Stream Endpoint
@router.get("/{video_id}/stream")
async def stream_video(video_id: int, session: Session = Depends(get_session)):
    """
    Stream the video file.
    """
    video = session.get(Video, video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    video_path = os.path.join(UPLOAD_DIR, video.file_path)
    if not os.path.exists(video_path):
        raise HTTPException(status_code=404, detail="Video file not found")
    
    # Basic file response (may not support seeking well)
    # For proper streaming with seeking, need to handle Range headers
    return FileResponse(video_path, media_type="video/mp4") # Assuming mp4
