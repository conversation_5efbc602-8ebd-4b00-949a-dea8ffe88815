from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import Session, select
from datetime import datetime, timedelta
import logging

from ..models.models import AnalysisJob, AnalysisJobRead, JobStatus
from ..utils.db import get_session

# Configure logging
logger = logging.getLogger(__name__)

router = APIRouter(tags=["analysis"])


@router.get("/", response_model=List[AnalysisJobRead])
def get_analysis_jobs(
    *,
    session: Session = Depends(get_session),
    skip: int = 0,
    limit: int = 100,
    status: Optional[JobStatus] = None,
    video_id: Optional[int] = None
):
    """
    Get a list of analysis jobs with optional filtering by status and video ID.
    """
    query = select(AnalysisJob)

    # Filter by status if provided
    if status:
        query = query.where(AnalysisJob.status == status)

    # Filter by video ID if provided
    if video_id:
        query = query.where(AnalysisJob.video_id == video_id)

    # Order by created_at in descending order to get the most recent jobs first
    query = query.order_by(AnalysisJob.created_at.desc())

    # Apply pagination
    query = query.offset(skip).limit(limit)

    jobs = session.exec(query).all()
    return jobs


@router.get("/{job_id}", response_model=AnalysisJobRead)
def get_analysis_job(*, session: Session = Depends(get_session), job_id: int):
    """
    Get a specific analysis job by ID.
    """
    job = session.get(AnalysisJob, job_id)
    if not job:
        raise HTTPException(status_code=404, detail="Analysis job not found")
    return job


@router.post("/check-stuck-jobs")
def check_stuck_jobs(*, session: Session = Depends(get_session)):
    """
    Check for stuck jobs and mark them as failed.
    A job is considered stuck if it has been in PROCESSING state for more than 30 minutes.
    """
    # Calculate the cutoff time (30 minutes ago)
    cutoff_time = datetime.utcnow() - timedelta(minutes=30)

    # Find jobs that have been in PROCESSING state for too long
    query = select(AnalysisJob).where(
        AnalysisJob.status == JobStatus.PROCESSING,
        AnalysisJob.started_at < cutoff_time
    )

    stuck_jobs = session.exec(query).all()

    # Mark stuck jobs as failed
    for job in stuck_jobs:
        logger.warning(f"Found stuck job ID {job.id} for video ID {job.video_id}, marking as failed")
        job.status = JobStatus.FAILED.value
        job.error_message = "Job timed out after 30 minutes"
        job.completed_at = datetime.utcnow()
        session.add(job)

    # Commit changes if any jobs were updated
    if stuck_jobs:
        session.commit()
        logger.info(f"Marked {len(stuck_jobs)} stuck jobs as failed")

    return {"message": f"Checked for stuck jobs, found and fixed {len(stuck_jobs)} stuck jobs"}
