from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import Session, select
from sqlalchemy.exc import IntegrityError

from ..models.models import Tag, TagCreate, TagRead, TagUpdate
from ..utils.db import get_session

router = APIRouter()


@router.get("/", response_model=List[TagRead])
def get_tags(
    session: Session = Depends(get_session),
    skip: int = 0,
    limit: int = 100,
    is_ai_generated: Optional[bool] = None
):
    """
    Get a list of tags with optional filtering by AI generation status.
    """
    query = select(Tag)

    # Filter by AI generation status if provided
    if is_ai_generated is not None:
        query = query.where(Tag.is_ai_generated == is_ai_generated)

    # Apply pagination
    query = query.offset(skip).limit(limit)

    tags = session.exec(query).all()
    return tags


@router.get("/{tag_id}", response_model=TagRead)
def get_tag(*, session: Session = Depends(get_session), tag_id: int):
    """
    Get a specific tag by ID.
    """
    tag = session.get(Tag, tag_id)
    if not tag:
        raise HTTPException(status_code=404, detail="Tag not found")
    return tag


@router.post("/", response_model=TagRead)
def create_tag(*, session: Session = Depends(get_session), tag: TagCreate):
    """
    Create a new tag.
    """
    # Create a new Tag instance directly from the TagCreate model
    db_tag = Tag(
        name=tag.name,
        description=tag.description,
        is_ai_generated=tag.is_ai_generated if tag.is_ai_generated is not None else False
    )
    session.add(db_tag)
    try:
        session.commit()
        session.refresh(db_tag)
        return db_tag
    except IntegrityError as e:
        session.rollback()
        raise HTTPException(status_code=409, detail="Tag with this name already exists.")


@router.patch("/{tag_id}", response_model=TagRead)
def update_tag(
    *, session: Session = Depends(get_session), tag_id: int, tag: TagUpdate
):
    """
    Update a tag.
    """
    db_tag = session.get(Tag, tag_id)
    if not db_tag:
        raise HTTPException(status_code=404, detail="Tag not found")

    tag_data = tag.dict(exclude_unset=True)
    for key, value in tag_data.items():
        setattr(db_tag, key, value)

    session.add(db_tag)
    session.commit()
    session.refresh(db_tag)
    return db_tag


@router.delete("/{tag_id}")
def delete_tag(*, session: Session = Depends(get_session), tag_id: int):
    """
    Delete a tag.
    """
    tag = session.get(Tag, tag_id)
    if not tag:
        raise HTTPException(status_code=404, detail="Tag not found")

    session.delete(tag)
    session.commit()
    return {"ok": True}
