from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlmodel import Session, select

from ..models.models import Video, Tag
from ..utils.db import get_session

router = APIRouter()


@router.post("/{video_id}/tags/{tag_id}")
def add_tag_to_video(
    *, session: Session = Depends(get_session), video_id: int, tag_id: int
):
    """
    Add a tag to a video.
    """
    video = session.get(Video, video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    tag = session.get(Tag, tag_id)
    if not tag:
        raise HTTPException(status_code=404, detail="Tag not found")
    
    # Check if tag is already associated
    if tag in video.tags:
        return {"message": "Tag already associated with video"}

    # Add tag to video's tag list
    video.tags.append(tag)
    session.add(video)
    session.commit()
    session.refresh(video) # Refresh to load the relationship

    return {"message": "Tag added to video"}


@router.delete("/{video_id}/tags/{tag_id}")
def remove_tag_from_video(
    *, session: Session = Depends(get_session), video_id: int, tag_id: int
):
    """
    Remove a tag from a video.
    """
    video = session.get(Video, video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")
    
    tag = session.get(Tag, tag_id)
    if not tag:
        raise HTTPException(status_code=404, detail="Tag not found")
    
    # Check if tag is associated
    if tag not in video.tags:
        return {"message": "Tag not associated with video"}

    # Remove tag from video's tag list
    video.tags.remove(tag)
    session.add(video)
    session.commit()
    session.refresh(video) # Refresh to confirm removal

    return {"message": "Tag removed from video"}
