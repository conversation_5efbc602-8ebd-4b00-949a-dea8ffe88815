"""Worker module for video analysis tasks."""
import os
import json
import time
import subprocess
import logging
import cv2
import numpy as np
import requests
from datetime import datetime
from typing import Optional, Tu<PERSON>
from sqlmodel import Session, select
import torch
from transformers import CLIPProcessor, CLIPModel
from ..models.models import Video, Tag, AnalysisJob, JobStatus
from ..utils.db import engine, get_session

# Try to import Llama, but don't fail if it's not available
try:
    from llama_cpp import Llama
    LLAMA_AVAILABLE = True
except ImportError:
    LLAMA_AVAILABLE = False
    print("Warning: llama-cpp-python not available. Topic generation will be limited.")

# Environment variables
UPLOAD_DIR = os.getenv("VIDEOS_DIR", "/videos")
models_dir = os.getenv("MODELS_DIR", "/models")
ai_analyzer_url = os.getenv("AI_ANALYZER_URL", "http://ai-analyzer:8080")
device = os.getenv("DEVICE", "cpu")  # 'cpu' or 'cuda'

# Initialize models
clip_model = None
clip_processor = None
llama_model = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def load_models():
    global clip_model, clip_processor, llama_model

    # Load CLIP model
    if clip_model is None or clip_processor is None:
        try:
            clip_model = CLIPModel.from_pretrained("openai/clip-vit-base-patch32")
            clip_processor = CLIPProcessor.from_pretrained("openai/clip-vit-base-patch32")

            # Move model to GPU if available
            if device == "cuda" and torch.cuda.is_available():
                clip_model = clip_model.to("cuda")
        except Exception as e:
            print(f"Error loading CLIP model: {e}")

    # Load Llama model if available
    if LLAMA_AVAILABLE and llama_model is None:
        try:
            llama_model_path = os.path.join(models_dir, "llama-2-7b-chat.Q4_K_M.gguf")
            if os.path.exists(llama_model_path):
                llama_model = Llama(
                    model_path=llama_model_path,
                    n_ctx=2048,
                    n_gpu_layers=-1 if device == "cuda" and torch.cuda.is_available() else 0
                )
            else:
                print(f"Llama model file not found at {llama_model_path}")
        except Exception as e:
            print(f"Error loading Llama model: {e}")


def analyze_video_task(video_id: int) -> dict:
    """Process a video through the AI pipeline."""
    logger.info(f"Starting analysis for video ID: {video_id}")

    # Get video from database
    with Session(engine) as session:
        video = session.get(Video, video_id)
        if not video:
            logger.error(f"Video with ID {video_id} not found")
            return {"error": "Video not found"}

        # Create or update analysis job
        job = AnalysisJob(
            video_id=video_id,
            status=JobStatus.PROCESSING.value
        )
        session.add(job)
        session.commit()

        try:
            # Extract transcript
            transcript, language = extract_transcript(video.file_path)

            # Update video with transcript and language
            video.transcript = transcript
            video.language = language
            session.add(video)

            # Mark job as complete
            job.status = JobStatus.COMPLETED.value
            session.add(job)
            session.commit()

            return {
                "success": True,
                "video_id": video_id,
                "transcript": transcript,
                "language": language
            }

        except Exception as e:
            logger.error(f"Error processing video {video_id}: {str(e)}")
            job.status = JobStatus.FAILED.value
            job.error_message = str(e)
            session.add(job)
            session.commit()
            return {"error": str(e)}


def extract_frames(video_path):
    """
    Extract 8 evenly-spaced frames from the video.
    """
    frames = []

    # Open the video file
    cap = cv2.VideoCapture(video_path)

    # Get video properties
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    duration = frame_count / fps if fps > 0 else 0

    # Calculate frame indices to extract
    if frame_count <= 8:
        # If there are fewer than 8 frames, use all of them
        frame_indices = list(range(frame_count))
    else:
        # Extract 8 evenly-spaced frames
        frame_indices = [int(i * frame_count / 8) for i in range(8)]

    # Extract the frames
    for idx in frame_indices:
        cap.set(cv2.CAP_PROP_POS_FRAMES, idx)
        ret, frame = cap.read()
        if ret:
            # Convert BGR to RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            frames.append(frame_rgb)

    # Release the video capture
    cap.release()

    return frames


def generate_frame_labels(frame):
    """
    Generate top-3 labels for a frame using the CLIP model.
    """
    # List of common objects and concepts for CLIP to classify
    candidate_labels = [
        "person", "dance", "singing", "talking", "outdoors", "indoors",
        "food", "animal", "pet", "music", "concert", "sports", "gaming",
        "makeup", "fashion", "comedy", "tutorial", "cooking", "fitness",
        "travel", "nature", "city", "beach", "party", "celebration",
        "challenge", "trend", "reaction", "review", "unboxing", "prank",
        "art", "craft", "DIY", "technology", "product", "advertisement",
        "educational", "informative", "news", "politics", "entertainment"
    ]

    # Process the image with CLIP
    inputs = clip_processor(
        text=candidate_labels,
        images=frame,
        return_tensors="pt",
        padding=True
    )

    # Move inputs to the same device as the model
    if device == "cuda" and torch.cuda.is_available():
        inputs = {k: v.to("cuda") for k, v in inputs.items()}

    # Get the model's predictions
    with torch.no_grad():
        outputs = clip_model(**inputs)
        logits_per_image = outputs.logits_per_image
        probs = logits_per_image.softmax(dim=1)

    # Get the top 3 predictions
    top_probs, top_indices = probs[0].topk(3)

    # Convert to list of (label, probability) tuples
    top_labels = [
        {
            "label": candidate_labels[idx],
            "probability": float(prob)
        }
        for prob, idx in zip(top_probs.cpu().numpy(), top_indices.cpu().numpy())
    ]

    return top_labels


def extract_transcript(video_path: str, max_retries: int = 4) -> Tuple[Optional[str], Optional[str]]:
    """Extract transcript from video using whisper-asr-webservice.

    Args:
        video_path: Path to the video file
        max_retries: Maximum number of retry attempts for transcription

    Returns:
        Tuple of (transcript, detected_language)
    """
    # Always use full path for video and audio
    if not os.path.isabs(video_path):
        video_path_full = os.path.join(UPLOAD_DIR, video_path)
    else:
        video_path_full = video_path
    audio_path = f"{os.path.splitext(video_path_full)[0]}.wav"

    try:
        # Convert video to audio using ffmpeg with improved error handling
        logging.info(f"Converting video to audio: {video_path_full} -> {audio_path}")
        try:
            cmd = [
                "ffmpeg", "-y", "-i", video_path_full,
                "-vn", "-acodec", "pcm_s16le",
                "-ar", "16000", "-ac", "1",
                audio_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, check=False)
            if result.returncode != 0:
                logging.error(f"Failed to convert video to audio: {result.stderr}")
                # Try with a different approach for problematic videos
                logging.info("Trying alternative conversion method...")
                alt_cmd = [
                    "ffmpeg", "-y", "-i", video_path_full,
                    "-vn", "-acodec", "pcm_s16le",
                    "-ar", "16000", "-ac", "1", "-af", "loudnorm",
                    audio_path
                ]
                alt_result = subprocess.run(alt_cmd, capture_output=True, text=True, check=False)
                if alt_result.returncode != 0:
                    logging.error(f"Alternative conversion also failed: {alt_result.stderr}")
                    return None, None
                logging.info("Alternative conversion successful")
        except Exception as e:
            logging.error(f"Exception during audio conversion: {str(e)}")
            return None, None

        # Enhanced Spanish detection based on filename or path
        spanish_terms = ['spanish', 'espanol', 'español', 'valenbrau', 'tiipsdehoy',
                         'latino', 'latina', 'mexico', 'méxico', 'argentina', 'colombia',
                         'peru', 'perú', 'chile', 'venezuela', 'cuba', 'dominicana']
        is_spanish = False

        # Log the video path for debugging
        logging.info(f"Checking for Spanish content in path: {video_path_full}")

        for term in spanish_terms:
            if term in video_path_full.lower():
                is_spanish = True
                logging.info(f"Spanish term '{term}' found in path")
                break

        # Set base parameters for transcription
        params = {
            'task': 'transcribe',
            'timeout': 900  # Increase timeout for longer videos (15 minutes)
        }

        # If we suspect Spanish content, use the medium model and Spanish-specific optimizations
        if is_spanish:
            logging.info("Spanish content detected, using optimized Spanish configuration")
            params.update({
                'language': 'es',
                'model_size': 'medium',  # Use the medium model for better Spanish support
                'temperature': 0.2,      # Allow more exploration for Spanish
                'beam_size': 8,          # Larger beam search for better results
                'patience': 2.0,         # More patience for beam search
                'initial_prompt': "Este es un video en español. Transcripción completa:"  # Help with context
            })
        else:
            logging.info("No Spanish content detected in filename, using standard configuration")

        # Try multiple times with increasing timeouts
        transcript = None
        language = None
        last_error = None

        for attempt in range(max_retries + 1):
            try:
                # Send audio to whisper service for transcription
                logging.info(f"Sending audio to whisper service for transcription (attempt {attempt+1}/{max_retries+1})")

                # Calculate timeout with exponential backoff
                timeout = 120 * (attempt + 1)  # 120s, 240s, 360s
                logging.info(f"Using timeout of {timeout} seconds")

                # Send the audio file to the enhanced whisper service
                with open(audio_path, 'rb') as f:
                    files = {'audio_file': f}

                    # Log the request parameters
                    logging.info(f"Sending request to whisper service with params: {params}")

                    # Choose the appropriate whisper service based on language
                    whisper_service_url = 'http://whisper-cpp-spanish:9000/asr' if is_spanish else 'http://whisper-cpp:9000/asr'
                    logging.info(f"Using whisper service: {whisper_service_url}")

                    # Send the request to the whisper service
                    response = requests.post(
                        whisper_service_url,
                        files=files,
                        params=params,
                        timeout=timeout  # Increasing timeout with each retry
                    )

                # Check if we got a 202 Accepted response (async processing)
                if response.status_code == 202:
                    # Extract the task ID
                    try:
                        data = response.json()
                        task_id = data.get("id")
                        logging.info(f"Whisper service accepted task with ID: {task_id}")

                        # Poll for the result
                        poll_start = time.time()
                        poll_interval = 5  # Start with 5 seconds
                        max_poll_time = timeout - 30  # Leave 30 seconds buffer

                        while time.time() - poll_start < max_poll_time:
                            try:
                                # Check the status of the task using the same service
                                whisper_status_url = f'http://whisper-cpp-spanish:9000/status/{task_id}' if is_spanish else f'http://whisper-cpp:9000/status/{task_id}'
                                status_response = requests.get(
                                    whisper_status_url,
                                    timeout=30
                                )

                                if status_response.status_code == 200:
                                    status_data = status_response.json()
                                    status = status_data.get("status")

                                    if status == "completed":
                                        # We have a result
                                        transcript = status_data.get("text", "").strip()
                                        language = status_data.get("language", None)

                                        logging.info(f"Task {task_id} completed successfully")
                                        logging.info(f"Transcript length: {len(transcript)}")
                                        logging.info(f"Detected language: {language}")

                                        if transcript:
                                            break  # Exit the polling loop
                                        else:
                                            logging.warning(f"Empty transcript received from task {task_id}")

                                    elif status == "error":
                                        # Task failed
                                        error_msg = status_data.get("error", "Unknown error")
                                        logging.error(f"Task {task_id} failed: {error_msg}")
                                        last_error = error_msg
                                        break  # Exit the polling loop

                                    else:
                                        # Task still processing
                                        logging.info(f"Task {task_id} still processing, status: {status}")

                                elif status_response.status_code == 404:
                                    # Task not found
                                    logging.error(f"Task {task_id} not found")
                                    last_error = "Task not found"
                                    break  # Exit the polling loop

                                else:
                                    # Other error
                                    logging.error(f"Error checking task status: {status_response.status_code}")
                                    last_error = f"Status check error: {status_response.status_code}"

                            except Exception as e:
                                logging.error(f"Error polling for task status: {str(e)}")
                                last_error = str(e)

                            # Wait before polling again
                            time.sleep(poll_interval)
                            # Increase the polling interval (up to 30 seconds)
                            poll_interval = min(poll_interval * 1.5, 30)

                        # If we have a transcript, break the retry loop
                        if transcript:
                            logging.info(f"Successfully extracted transcript from task {task_id}")
                            break
                        else:
                            logging.warning(f"Failed to get transcript from task {task_id}")
                            # Continue to next attempt if we have retries left
                            if attempt < max_retries:
                                time.sleep(5)  # Wait before retrying
                                continue

                    except Exception as e:
                        logging.error(f"Error processing async task: {str(e)}")
                        last_error = str(e)
                        # Continue to next attempt if we have retries left
                        if attempt < max_retries:
                            time.sleep(5)  # Wait before retrying
                            continue

                # Handle direct response (200 OK)
                elif response.status_code == 200:
                    # Extract transcript and language
                    try:
                        data = response.json()
                        transcript = data.get("text", "").strip()
                        language = data.get("language", None)

                        # Check if this is a temporary response while models are loading
                        if data.get("loading", False):
                            logging.warning(f"Whisper service returned temporary response: {data.get('message', 'Models are still loading')}")
                            # If this is the last attempt, use the temporary transcript
                            if attempt == max_retries:
                                logging.info(f"Using temporary transcript after {attempt+1} attempts")
                                break
                            else:
                                # Wait longer before retrying when models are loading
                                logging.info(f"Models are still loading, waiting 10 seconds before retry")
                                time.sleep(10)  # Wait longer before retrying
                                continue

                        # Log the full response for debugging
                        logging.info(f"Whisper direct response: {data}")

                        # If we got a valid transcript, break the retry loop
                        if transcript:
                            logging.info(f"Successfully extracted transcript on attempt {attempt+1}")
                            break
                        else:
                            logging.warning(f"Empty transcript received on attempt {attempt+1}")
                            # Continue to next attempt if we have retries left
                            if attempt < max_retries:
                                time.sleep(5)  # Wait before retrying
                                continue

                    except Exception as e:
                        logging.error(f"Error parsing Whisper response: {str(e)}")
                        last_error = str(e)
                        # Continue to next attempt if we have retries left
                        if attempt < max_retries:
                            time.sleep(5)  # Wait before retrying
                            continue

                # Handle error responses
                else:
                    logging.error(f"Whisper service returned error {response.status_code}: {response.text}")
                    last_error = f"HTTP error {response.status_code}"
                    # Continue to next attempt if we have retries left
                    if attempt < max_retries:
                        time.sleep(5)  # Wait before retrying
                        continue
                    else:
                        break

            except Exception as e:
                logging.error(f"Error parsing Whisper response: {str(e)}")
                last_error = str(e)
                # Try to get the raw text
                try:
                    transcript = response.text.strip()
                    logging.warning(f"Falling back to raw text: {transcript[:100]}...")
                    if transcript:
                        break
                except:
                    pass
                # Continue to next attempt if we have retries left
                if attempt < max_retries:
                    time.sleep(5)  # Wait before retrying
                    continue

            except requests.RequestException as e:
                logging.error(f"Failed to communicate with whisper service (attempt {attempt+1}): {e}")
                last_error = str(e)
                # Continue to next attempt if we have retries left
                if attempt < max_retries:
                    time.sleep(2)  # Wait before retrying
                    continue

        # If we detected Spanish from the filename, force the language
        if is_spanish and not language:
            language = 'es'
            logging.info("Forcing language to Spanish based on filename")

        # Log the final result
        if transcript:
            logging.info(f"Successfully extracted transcript ({len(transcript)} chars) in language: {language}")
        else:
            logging.warning(f"Failed to extract transcript after {max_retries+1} attempts: {last_error}")
            # For Spanish videos, provide a more detailed fallback message
            if is_spanish:
                transcript = "No se pudo obtener la transcripción completa de este video en español. " + \
                             "El sistema ha detectado que el contenido está en español pero necesita más tiempo para procesarlo. " + \
                             "Por favor, inténtelo de nuevo más tarde o contacte con soporte técnico si el problema persiste."
                language = 'es'
            else:
                transcript = "Could not transcribe this video. The system needs more time to process the content. " + \
                             "Please try again later or contact technical support if the problem persists."

        return transcript, language

    except Exception as e:
        logging.error(f"Unexpected error in extract_transcript: {str(e)}")
        # For Spanish videos, provide a fallback message
        if 'is_spanish' in locals() and is_spanish:
            return "Error al transcribir este video. Por favor, inténtelo de nuevo más tarde.", 'es'
        else:
            return "Error transcribing this video. Please try again later.", None
    finally:
        # Cleanup temporary audio file
        if os.path.exists(audio_path):
            try:
                os.remove(audio_path)
                logging.info(f"Cleaned up temporary audio file: {audio_path}")
            except Exception as e:
                logging.warning(f"Failed to cleanup audio file {audio_path}: {str(e)}")


def generate_summary_paragraph(frame_labels, transcript):
    """
    Generate a summary paragraph from frame labels and transcript.
    """
    # Extract unique labels from all frames
    all_labels = set()
    for frame in frame_labels:
        for label_info in frame["labels"]:
            all_labels.add(label_info["label"])

    # Create a summary paragraph
    summary = "This video contains: " + ", ".join(all_labels)

    # Add transcript summary if available
    if transcript:
        # Truncate transcript if it's too long
        max_transcript_length = 500
        transcript_summary = transcript[:max_transcript_length]
        if len(transcript) > max_transcript_length:
            transcript_summary += "..."

        summary += f"\n\nTranscript: {transcript_summary}"

    return summary


def generate_topic_and_keywords(summary_paragraph):
    """
    Generate the main topic and keywords using the Llama model if available,
    otherwise use a simple rule-based approach with improved filtering.
    """
    # If Llama is not available or model is not loaded, use a simple approach
    if not LLAMA_AVAILABLE or llama_model is None:
        print("Using improved fallback topic and keyword generation (Llama model not available)")
        # Extract potential keywords from the summary
        import re
        words = re.findall(r"\\b\\w+\\b", summary_paragraph.lower())
        # Remove common stopwords and generic words
        stopwords = set([
            'this', 'video', 'contains', 'about', 'with', 'that', 'from', 'have', 'will', 'your', 'just',
            'they', 'their', 'which', 'would', 'there', 'what', 'when', 'where', 'were', 'been', 'more',
            'than', 'some', 'them', 'into', 'only', 'other', 'also', 'could', 'should', 'very', 'like',
            'because', 'while', 'such', 'each', 'most', 'many', 'much', 'even', 'over', 'under', 'again',
            'those', 'these', 'for', 'and', 'the', 'are', 'was', 'but', 'not', 'you', 'all', 'can', 'any',
            'our', 'out', 'who', 'how', 'why', 'get', 'got', 'see', 'use', 'used', 'using', 'make', 'made',
            'do', 'did', 'does', 'doing', 'an', 'a', 'of', 'in', 'on', 'to', 'as', 'by', 'at', 'is', 'it',
            'be', 'or', 'if', 'so', 'no', 'yes', 'up', 'down', 'off', 'on', 'we', 'he', 'she', 'his', 'her',
            'i', 'my', 'me', 'you', 'your', 'yours', 'ours', 'theirs', 'ourselves', 'themselves', 'him', 'himself',
            'hers', 'herself', 'itself', 'themselves', 'ourselves', 'yourself', 'yourselves', 'himself', 'herself',
            'itself', 'ourselves', 'themselves', 'challenge', 'review', 'unboxing', 'prank', 'trend', 'reaction',
            'transcript'
        ])
        filtered_words = [w for w in words if len(w) > 3 and w not in stopwords]
        # Count word frequency
        word_counts = {}
        for word in filtered_words:
            if word in word_counts:
                word_counts[word] += 1
            else:
                word_counts[word] = 1
        # Sort by frequency
        sorted_words = sorted(word_counts.items(), key=lambda x: x[1], reverse=True)
        # Get top 5 keywords
        keywords = [word for word, count in sorted_words[:5]]
        # Ensure we have exactly 5 keywords
        while len(keywords) < 5:
            keywords.append(f"keyword{len(keywords)+1}")
        # Generate a simple topic
        if keywords:
            topic = f"Video about {keywords[0]}"
        else:
            topic = "Unknown topic"
        return topic, keywords

    # Use Llama model if available
    # Create the prompt for the Llama model
    prompt = f"""
You are an AI assistant that analyzes TikTok videos.
Based on the following information about a video, summarize the main topic in a short phrase (max 10 words) and provide 5 comma-separated keywords.

Information about the video:
{summary_paragraph}

Format your response exactly like this:
Topic: [main topic]
Keywords: [keyword1], [keyword2], [keyword3], [keyword4], [keyword5]
"""

    # Generate response from the Llama model
    try:
        response = llama_model(
            prompt,
            max_tokens=256,
            stop=["</s>"],
            temperature=0.7
        )

        # Extract the generated text
        generated_text = response["choices"][0]["text"]

        # Parse the topic and keywords from the response
        topic = ""
        keywords = []

        for line in generated_text.strip().split("\n"):
            if line.startswith("Topic:"):
                topic = line.replace("Topic:", "").strip()
            elif line.startswith("Keywords:"):
                keywords_text = line.replace("Keywords:", "").strip()
                keywords = [k.strip() for k in keywords_text.split(",") if k.strip()]

        # Ensure we have exactly 5 keywords
        while len(keywords) < 5:
            keywords.append(f"keyword{len(keywords)+1}")

        # Limit to 5 keywords if we have more
        keywords = keywords[:5]

        return topic, keywords

    except Exception as e:
        print(f"Error generating topic and keywords: {e}")
        return "Unknown topic", ["keyword1", "keyword2", "keyword3", "keyword4", "keyword5"]
