from celery import Celery
import time
import os
import logging
import requests
from sqlmodel import Session, select
from datetime import datetime, timedelta
from typing import Optional, Tuple
import subprocess

# Get environment variables
redis_url = os.getenv('REDIS_URL', 'redis://redis:6379/0')
UPLOAD_DIR = os.getenv("VIDEOS_DIR", "/videos")

# Initialize Celery app
celery_app = Celery(
    'src.tasks',
    broker=redis_url,
    backend=redis_url,
    task_serializer='json',
    result_serializer='json',
    accept_content=['json']
)

# Configure Celery
celery_app.conf.update(
    timezone='UTC',
    enable_utc=True,
    beat_schedule={
        'check-stuck-jobs': {
            'task': 'src.tasks.tasks.check_stuck_jobs',
            'schedule': 300.0,  # Run every 5 minutes
        }
    }
)

# Import models and database utilities
from ..models.models import Video, AnalysisJob, JobStatus, Tag
from ..utils.db import engine, get_session
from .worker import extract_transcript

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@celery_app.task
def placeholder_task(x, y):
    time.sleep(5)
    print(f"Placeholder task running: {x} + {y}")
    return x + y


@celery_app.task
def check_stuck_jobs():
    """
    Check for stuck jobs and mark them as failed.
    A job is considered stuck if it has been in PROCESSING state for more than 30 minutes.
    """
    logger.info("Running scheduled check for stuck jobs")

    # Calculate the cutoff time (30 minutes ago)
    cutoff_time = datetime.utcnow() - timedelta(minutes=30)

    with Session(engine) as session:
        # Find jobs that have been in PROCESSING state for too long
        query = select(AnalysisJob).where(
            AnalysisJob.status == JobStatus.PROCESSING,
            AnalysisJob.started_at < cutoff_time
        )

        stuck_jobs = session.exec(query).all()

        # Mark stuck jobs as failed
        for job in stuck_jobs:
            logger.warning(f"Found stuck job ID {job.id} for video ID {job.video_id}, marking as failed")
            job.status = JobStatus.FAILED.value
            job.error_message = "Job timed out after 30 minutes"
            job.completed_at = datetime.utcnow()
            session.add(job)

        # Commit changes if any jobs were updated
        if stuck_jobs:
            session.commit()
            logger.info(f"Marked {len(stuck_jobs)} stuck jobs as failed")
        else:
            logger.info("No stuck jobs found")

    return {"message": f"Checked for stuck jobs, found and fixed {len(stuck_jobs)} stuck jobs"}

@celery_app.task(bind=True, max_retries=5)  # Increased max retries
def analyze_video(self, video_id: int):
    """Analyze a video using AI pipeline with improved error handling and retries."""
    logger.info(f"Starting analysis for video ID: {video_id}")

    # Get video from database
    with Session(engine) as session:
        video = session.get(Video, video_id)
        if not video:
            logger.error(f"Video with ID {video_id} not found")
            return {"error": "Video not found"}

        # Check if there's already a processing job for this video
        existing_job = session.exec(
            select(AnalysisJob)
            .where(AnalysisJob.video_id == video_id)
            .where(AnalysisJob.status == JobStatus.PROCESSING)
        ).first()

        if existing_job:
            logger.info(f"Found existing processing job for video {video_id}, updating it")
            job = existing_job
            job.started_at = datetime.utcnow()  # Reset the start time
        else:
            # Create a new analysis job
            job = AnalysisJob(
                video_id=video_id,
                status=JobStatus.PROCESSING.value,
                started_at=datetime.utcnow()
            )
            session.add(job)

        session.commit()
        session.refresh(job)
        job_id = job.id

        try:
            # Check if the file exists
            file_path = video.file_path
            full_path = os.path.join("/videos", file_path) if not os.path.isabs(file_path) else file_path

            if not os.path.exists(full_path):
                logger.error(f"Video file not found: {full_path}")
                raise Exception(f"Video file not found: {file_path}")

            # Check if this is a Spanish video based on filename
            is_spanish = any(spanish_term in file_path.lower()
                            for spanish_term in ['spanish', 'espanol', 'español', 'valenbrau', 'tiipsdehoy'])

            # Extract transcript with retries
            logger.info(f"Extracting transcript for video {video_id} (file: {file_path})")
            logger.info(f"Is Spanish content: {is_spanish}")

            # Use max_retries parameter for extract_transcript
            result = extract_transcript(video.file_path, max_retries=3)  # Increased retries

            if result is None or (isinstance(result, tuple) and result[0] is None):
                logger.error(f"Transcript extraction failed for video {video_id}")

                # For Spanish videos, try a direct approach with ffmpeg and whisper
                if is_spanish:
                    logger.info(f"Attempting direct transcription for Spanish video {video_id}")
                    try:
                        # Create a temporary WAV file
                        temp_wav = f"/tmp/direct_spanish_{video_id}.wav"
                        full_path = os.path.join(UPLOAD_DIR, video.file_path)

                        # Extract audio with ffmpeg
                        cmd = [
                            "ffmpeg", "-y", "-i", full_path,
                            "-vn", "-acodec", "pcm_s16le",
                            "-ar", "16000", "-ac", "1",
                            temp_wav
                        ]
                        subprocess.run(cmd, check=True, capture_output=True)

                        # Send directly to whisper with Spanish-specific parameters
                        with open(temp_wav, 'rb') as f:
                            files = {'audio_file': f}
                            params = {
                                'task': 'transcribe',
                                'language': 'es',
                                'temperature': 0.2,
                                'beam_size': 8,
                                'initial_prompt': 'Este es un video en español.',
                                'timeout': 600
                            }
                            response = requests.post(
                                'http://whisper-cpp:9000/asr',
                                files=files,
                                params=params,
                                timeout=600  # 10 minute timeout
                            )

                        # Process response
                        if response.status_code == 200:
                            data = response.json()
                            result = (data.get("text", ""), "es")
                            logger.info(f"Direct Spanish transcription successful: {len(result[0])} chars")
                        else:
                            logger.error(f"Direct Spanish transcription failed: {response.status_code}")

                        # Clean up
                        if os.path.exists(temp_wav):
                            os.remove(temp_wav)

                    except Exception as e:
                        logger.error(f"Error in direct Spanish transcription: {str(e)}")

                # If still no result, retry the task
                if result is None or (isinstance(result, tuple) and result[0] is None):
                    # Retry the task if we haven't exceeded max retries
                    if self.request.retries < self.max_retries:
                        retry_delay = 60 * (2 ** self.request.retries)  # Exponential backoff
                        logger.info(f"Retrying transcript extraction for video {video_id} (attempt {self.request.retries + 1}) in {retry_delay} seconds")
                        raise self.retry(countdown=retry_delay)
                    else:
                        raise Exception("Failed to extract transcript after multiple attempts")

            transcript, language = result

            # Log detailed information about the result
            logger.info(f"Successfully extracted transcript for video {video_id}")
            logger.info(f"Transcript length: {len(transcript) if transcript else 0} characters")
            logger.info(f"Detected language: {language}")

            # Check if we have a valid transcript
            if not transcript or len(transcript) < 5:
                logger.warning(f"Very short or empty transcript for video {video_id}")

                # For Spanish videos, provide a fallback message if needed
                if language == 'es' or 'valenbrau' in file_path.lower():
                    if not transcript:
                        transcript = "No se pudo transcribir completamente este video. Por favor, inténtelo de nuevo más tarde."
                        language = 'es'
                        logger.info("Using Spanish fallback message for empty transcript")

            # Update video with transcript and language in a separate transaction to ensure it's saved
            try:
                # Update video with transcript and language
                video.transcript = transcript
                video.language = language
                session.add(video)
                session.commit()
                logger.info(f"Updated video {video_id} with transcript and language in database")
            except Exception as db_error:
                logger.error(f"Failed to update video {video_id} in database: {str(db_error)}")
                session.rollback()
                # Try one more time with a new session
                try:
                    with Session(engine) as new_session:
                        db_video = new_session.get(Video, video_id)
                        if db_video:
                            db_video.transcript = transcript
                            db_video.language = language
                            new_session.add(db_video)
                            new_session.commit()
                            logger.info(f"Successfully updated video {video_id} with new session")
                except Exception as retry_error:
                    logger.error(f"Failed to update video {video_id} even with new session: {str(retry_error)}")
                    # Continue with job update even if video update failed

            # Mark job as complete
            job.status = JobStatus.COMPLETED.value
            job.completed_at = datetime.utcnow()
            session.add(job)
            session.commit()

            logger.info(f"Analysis completed successfully for video {video_id}")
            return {
                "success": True,
                "video_id": video_id,
                "job_id": job_id,
                "transcript": transcript,
                "language": language
            }

        except self.retry as retry_exc:
            # This exception is raised when we want to retry the task
            raise retry_exc
        except Exception as e:
            logger.error(f"Error processing video {video_id}: {str(e)}")
            try:
                job.status = JobStatus.FAILED.value
                job.error_message = str(e)[:500]  # Limit error message length
                job.completed_at = datetime.utcnow()
                session.add(job)
                session.commit()
            except Exception as job_error:
                logger.error(f"Failed to update job status: {str(job_error)}")

            return {"error": str(e), "video_id": video_id, "job_id": job_id}