import os
from datetime import datetime
from typing import List, Optional

from sqlalchemy import Column, DateTime, Index, String, Text, func
from sqlmodel import Field, Relationship, SQLModel


# --- Link Table --- #

class VideoTag(SQLModel, table=True):
    """Many-to-many link table between Video and Tag."""
    video_id: Optional[int] = Field(
        default=None, foreign_key="video.id", primary_key=True
    )
    tag_id: Optional[int] = Field(
        default=None, foreign_key="tag.id", primary_key=True
    )


# --- Main Models --- #

class Tag(SQLModel, table=True):
    """Represents a keyword or tag."""
    id: Optional[int] = Field(default=None, primary_key=True)
    name: str = Field(index=True, unique=True, max_length=255)

    videos: List["Video"] = Relationship(back_populates="tags", link_model=VideoTag)

    created_at: datetime = Field(
        default_factory=datetime.utcnow, 
        sa_column=Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    )
    updated_at: datetime = Field(
        default_factory=datetime.utcnow,
        sa_column=Column(
            DateTime(timezone=True),
            server_default=func.now(),
            onupdate=func.now(),
            nullable=False
        )
    )


class Video(SQLModel, table=True):
    """Represents a downloaded video and its metadata."""
    id: Optional[int] = Field(default=None, primary_key=True)
    tiktok_id: Optional[str] = Field(default=None, index=True, unique=True, max_length=100)
    # Identifier from TikTok if available
    filepath: str = Field(index=True, unique=True, max_length=1024)
    # Relative path within the VIDEOS_DIR volume
    language: Optional[str] = Field(default=None, index=True, max_length=10)
    # Language of the video content (e.g., 'en', 'es', 'fr')
    downloaded_at: datetime = Field(
        default_factory=datetime.utcnow,
        sa_column=Column(DateTime(timezone=True), server_default=func.now(), index=True, nullable=False)
    )
    # AI Analysis Results
    analysis_job_id: Optional[int] = Field(default=None, foreign_key="analysisjob.id")
    analysis_job: Optional["AnalysisJob"] = Relationship(back_populates="video")
    analyzed_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime(timezone=True), nullable=True)
    )
    main_topic: Optional[str] = Field(default=None, sa_column=Column(Text))
    transcript: Optional[str] = Field(default=None, sa_column=Column(Text))
    # Timestamps not explicitly modeled yet as per idea.txt, add later if needed

    # Many-to-many relationship with Tag
    tags: List[Tag] = Relationship(back_populates="videos", link_model=VideoTag)

    created_at: datetime = Field(
        default_factory=datetime.utcnow,
        sa_column=Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    )
    updated_at: datetime = Field(
        default_factory=datetime.utcnow,
        sa_column=Column(
            DateTime(timezone=True),
            server_default=func.now(),
            onupdate=func.now(),
            nullable=False
        )
    )


class AnalysisJob(SQLModel, table=True):
    """Tracks the status of an AI analysis task for a video."""
    id: Optional[int] = Field(default=None, primary_key=True)
    celery_task_id: Optional[str] = Field(default=None, index=True, max_length=255)
    status: str = Field(default="PENDING", index=True, max_length=50)
    # e.g., PENDING, PROCESSING, SUCCESS, FAILED
    video_id: Optional[int] = Field(default=None, foreign_key="video.id")
    video: Optional[Video] = Relationship(back_populates="analysis_job")

    error_message: Optional[str] = Field(default=None, sa_column=Column(Text))

    created_at: datetime = Field(
        default_factory=datetime.utcnow,
        sa_column=Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    )
    updated_at: datetime = Field(
        default_factory=datetime.utcnow,
        sa_column=Column(
            DateTime(timezone=True),
            server_default=func.now(),
            onupdate=func.now(),
            nullable=False
        )
    )

    __table_args__ = (
        Index('ix_analysisjob_status_created_at', "status", "created_at"),
    ) 