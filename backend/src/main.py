from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from src.api import videos, tags, video_tags, analysis_jobs
from src.utils.db import create_db_and_tables
from src.worker import celery_app  # Import the Celery app instance
from celery import current_app
import redis
import time
import os

# Ensure the imported celery_app becomes the default for tasks
current_app.conf.update(celery_app.conf)

# Create a simple FastAPI app for testing
app = FastAPI(title="TagTok API", description="API for TagTok video analysis platform")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create database tables
create_db_and_tables()

# Include routers with unique prefixes
app.include_router(videos.router, prefix="/api/videos", tags=["videos"])
app.include_router(tags.router, prefix="/api/tags", tags=["tags"])
app.include_router(video_tags.router, prefix="/api/video-tags", tags=["video-tags"])
app.include_router(analysis_jobs.router, prefix="/api/analysis-jobs", tags=["analysis"])

@app.get("/api/health")
def health_check():
    return {"status": "ok"}

@app.get("/health")
def health_check_alt():
    return {"status": "ok"}

@app.on_event("startup")
def check_redis_connectivity():
    redis_url = os.getenv("REDIS_URL", "redis://redis:6379/0")
    for _ in range(10):
        try:
            r = redis.from_url(redis_url)
            r.ping()
            print("Redis is available.")
            return
        except Exception as e:
            print(f"Waiting for Redis... {e}")
            time.sleep(2)
    raise RuntimeError("Redis is not available after multiple attempts.")
