services:
  # PostgreSQL database
  postgres:
    image: postgres:15-alpine
    container_name: tagtok-postgres
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-tagtok}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-tagtokpass}
      POSTGRES_DB: ${POSTGRES_DB:-tagtok}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-tagtok} -d ${POSTGRES_DB:-tagtok}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - tagtok-network

  # Redis for Celery and caching
  redis:
    image: redis:7-alpine
    container_name: tagtok-redis
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - tagtok-network

  # FastAPI backend application
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: tagtok-backend
    volumes:
      - ./backend:/app
      - ./models:/models
      - ./videos:/videos
    environment:
      DATABASE_URL: postgresql://${POSTGRES_USER:-tagtok}:${POSTGRES_PASSWORD:-tagtokpass}@postgres:5432/${POSTGRES_DB:-tagtok}
      REDIS_URL: redis://redis:6379/0
      AI_ANALYZER_URL: http://ai-analyzer:8080
      VIDEOS_DIR: /videos
      PYTHONPATH: /app
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      whisper-cpp:
        condition: service_healthy
      whisper-cpp-spanish:
        condition: service_healthy
    # Correct command to run migrations and start the FastAPI app
    # Added a check to wait for Whisper services to be fully ready
    command: >
      bash -c "
        echo 'Waiting for Whisper services to be fully ready...' &&
        sleep 5 &&
        cd /app &&
        alembic upgrade head &&
        uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload
      "
    restart: unless-stopped
    networks:
      - tagtok-network

  # Celery worker for background tasks
  celery-worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: tagtok-celery-worker
    volumes:
      - ./backend:/app
      - ./models:/models
      - ./videos:/videos
    environment:
      DATABASE_URL: postgresql://${POSTGRES_USER:-tagtok}:${POSTGRES_PASSWORD:-tagtokpass}@postgres:5432/${POSTGRES_DB:-tagtok}
      REDIS_URL: redis://redis:6379/0
      AI_ANALYZER_URL: http://ai-analyzer:8080
      VIDEOS_DIR: /videos
      PYTHONPATH: /app
      # Add C_FORCE_ROOT to allow running Celery as root in container (use with caution)
      C_FORCE_ROOT: "true"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      whisper-cpp:
        condition: service_healthy
      whisper-cpp-spanish:
        condition: service_healthy
    # Command to start the Celery worker with proper Python path
    # Added a check to wait for Whisper services to be fully ready
    command: >
      bash -c "
        echo 'Waiting for Whisper services to be fully ready...' &&
        sleep 5 &&
        celery -A src.tasks.tasks worker --loglevel=info
      "
    restart: unless-stopped
    networks:
      - tagtok-network

  # Celery beat for scheduled tasks
  celery-beat:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: tagtok-celery-beat
    volumes:
      - ./backend:/app
    environment:
      DATABASE_URL: postgresql://${POSTGRES_USER:-tagtok}:${POSTGRES_PASSWORD:-tagtokpass}@postgres:5432/${POSTGRES_DB:-tagtok}
      REDIS_URL: redis://redis:6379/0
      PYTHONPATH: /app
      # Add C_FORCE_ROOT to allow running Celery as root in container (use with caution)
      C_FORCE_ROOT: "true"
    depends_on:
      - backend
      - redis
    # Command to start Celery beat with proper Python path
    command: bash -c "cd /app && celery -A src.worker.celery_app beat --loglevel=info"
    restart: unless-stopped
    networks:
      - tagtok-network

  # AI Analyzer service
  ai-analyzer:
    build:
      context: ./ai-analyzer
      dockerfile: Dockerfile
    container_name: tagtok-ai-analyzer
    volumes:
      - ./ai-analyzer:/app
      - ./videos:/videos
      - ./models:/models
    environment:
      VIDEOS_DIR: /videos
      MODELS_DIR: /models
      DEVICE: ${DEVICE:-cuda}  # Use CUDA by default
    ports:
      - "8080:8080"
    restart: unless-stopped
    networks:
      - tagtok-network

  # Video downloader service
  downloader:
    build:
      context: ./downloader
      dockerfile: Dockerfile
    container_name: tagtok-downloader
    volumes:
      - ./downloader:/app
      - ./videos:/videos
    environment:
      DATABASE_URL: postgresql://${POSTGRES_USER:-tagtok}:${POSTGRES_PASSWORD:-tagtokpass}@postgres:5432/${POSTGRES_DB:-tagtok}
      REDIS_URL: redis://redis:6379/0
      VIDEOS_DIR: /videos
    ports:
      - "8081:8081"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    # Correct the internal port for uvicorn to 8081
    command: bash -c "cd /app && uvicorn src.main:app --host 0.0.0.0 --port 8081 --reload"
    restart: unless-stopped
    networks:
      - tagtok-network

  # Frontend React application
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: tagtok-frontend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3009:80"
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - tagtok-network

  # Enhanced Whisper speech-to-text service with dedicated Spanish model
  whisper-cpp:
    build:
      context: .
      dockerfile: Dockerfile.whisper
    container_name: whisper-cpp
    volumes:
      - ./models:/models
      - ./videos:/videos
      - whisper-cache:/app/cache
    environment:
      - WHISPER_MODELS_DIR=/models
      - WHISPER_CACHE_DIR=/app/cache
      - REDIS_URL=redis://redis:6379/0
      - MAX_QUEUE_SIZE=20
      - WORKER_THREADS=8
      - LOAD_MEDIUM_MODEL=true
      - DEVICE=cuda
    ports:
      - "9000:9000"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9000/health || exit 0"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s  # Increased start period to allow more time for model loading
    deploy:
      resources:
        limits:
          cpus: '8'
          memory: 16G
        reservations:
          cpus: '4'
          memory: 8G
    depends_on:
      - redis
    networks:
      - tagtok-network

  # Second Whisper instance for load balancing (Spanish-focused)
  whisper-cpp-spanish:
    build:
      context: .
      dockerfile: Dockerfile.whisper
    container_name: whisper-cpp-spanish
    volumes:
      - ./models:/models
      - ./videos:/videos
      - whisper-cache-spanish:/app/cache
    environment:
      - WHISPER_MODELS_DIR=/models
      - WHISPER_CACHE_DIR=/app/cache
      - REDIS_URL=redis://redis:6379/0
      - MAX_QUEUE_SIZE=20
      - WORKER_THREADS=8
      - LOAD_MEDIUM_MODEL=true
      - OPTIMIZE_FOR_SPANISH=true
      - DEVICE=cuda
    ports:
      - "9001:9000"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9000/health || exit 0"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s  # Increased start period to allow more time for model loading
    deploy:
      resources:
        limits:
          cpus: '8'
          memory: 16G
        reservations:
          cpus: '4'
          memory: 8G
    depends_on:
      - redis
    networks:
      - tagtok-network

networks:
  tagtok-network:
    driver: bridge

volumes:
  postgres_data:
  models_data:
  whisper-models:  # Persistent storage for Whisper models
  whisper-cache:   # Cache for Whisper transcriptions
  whisper-models-spanish:  # Persistent storage for Spanish Whisper models
  whisper-cache-spanish:   # Cache for Spanish Whisper transcriptions
