# TagTok

TagTok is a self-hosted, cost-free web platform that lets you download, analyze, and neatly organize TikTok videos. It uses AI to automatically extract topics, generate keywords, and create searchable transcripts from your videos.

## Features

- **Video Download**: Download TikTok videos using yt-dlp and store them locally
- **AI-Powered Analysis**: Automatically analyze videos to extract:
  - Visual content using CLIP image recognition
  - Audio transcripts using Whisper
  - Topics and keywords using Llama 2/3
- **Clean Web Interface**: Browse, search, and filter your video collection
- **Tag Management**: Create, edit, and organize videos with tags
- **Containerized**: Everything runs in Docker containers for easy deployment

## System Requirements

- Docker and Docker Compose
- 4GB+ RAM (8GB+ recommended)
- 10GB+ free disk space (plus space for your videos)
- CPU-only mode works on any modern CPU
- GPU acceleration (optional) requires NVIDIA GPU with CUDA support

## Quick Start

### 1. Clone the Repository

```bash
git clone https://github.com/yourusername/tagtok.git
cd tagtok
```

### 2. Download AI Models (Optional)

For faster startup, you can pre-download the required AI models:

```bash
mkdir -p models
# Download Whisper model
wget -O models/ggml-base.en.bin https://huggingface.co/ggerganov/whisper.cpp/resolve/main/ggml-base.en.bin
# Download Llama model
wget -O models/llama-2-7b-chat.Q4_K_M.gguf https://huggingface.co/TheBloke/Llama-2-7B-Chat-GGUF/resolve/main/llama-2-7b-chat.Q4_K_M.gguf
```

### 3. Create Videos Directory

```bash
mkdir -p videos
```

### 4. Start the Application

```bash
docker compose up -d
```

This will start all services:
- PostgreSQL database
- Redis for task queue
- FastAPI backend
- Celery worker and beat
- AI Analyzer service
- React frontend

### 5. Access the Web Interface

Open your browser and navigate to:

```
http://localhost:3009
```

## Usage

### Downloading Videos

1. Navigate to the download page
2. Paste a TikTok video URL
3. Click "Download"

The video will be downloaded and automatically analyzed.

### Browsing Videos

- Use the sidebar to filter videos by tags and date range
- Use the search bar to find videos by title, content, or transcript
- Click on a video thumbnail to view details and play the video

### Managing Tags

- Add or remove tags from videos in the video detail modal
- Create new tags as needed
- AI-generated tags are marked with a different color

### Re-analyzing Videos

If you want to re-analyze a video:
1. Open the video details
2. Click "Re-analyze Video"

## Configuration

The application can be configured using environment variables in the `.env` file:

```
# Database configuration
POSTGRES_USER=tagtok
POSTGRES_PASSWORD=tagtokpass
POSTGRES_DB=tagtok

# AI configuration
DEVICE=cpu  # Change to 'cuda' for GPU acceleration

# Application settings
VIDEOS_DIR=/videos
MODELS_DIR=/models
```

## Troubleshooting

### CPU vs. GPU Inference

By default, TagTok runs in CPU-only mode. To enable GPU acceleration:

1. Make sure you have an NVIDIA GPU with CUDA support
2. Install the NVIDIA Container Toolkit
3. Set `DEVICE=cuda` in your `.env` file
4. Restart the containers: `docker compose down && docker compose up -d`

### Common Issues

#### Videos Not Appearing

- Check that your videos are in the correct directory
- Run the bootstrap script to scan for new videos:

```bash
docker compose exec backend python bootstrap.py
```

#### Analysis Failing

- Check the logs for errors:

```bash
docker compose logs celery-worker
```

- Make sure the AI models are downloaded correctly
- Try increasing the memory limit for the containers

#### Database Connection Issues

- Check the database logs:

```bash
docker compose logs postgres
```

- Ensure the database is healthy:

```bash
docker compose exec postgres pg_isready -U tagtok -d tagtok
```

## Advanced Usage

### Manual Database Seeding

To manually scan the videos directory and add new videos to the database:

```bash
docker compose exec backend python bootstrap.py
```

To force reanalysis of all videos:

```bash
docker compose exec backend python bootstrap.py --force-reanalysis
```

### Accessing the API Directly

The API is available at:

```
http://localhost:8000/api
```

API documentation is available at:

```
http://localhost:8000/docs
```

## Architecture

TagTok consists of several containerized services:

- **PostgreSQL**: Database for storing video metadata, tags, and analysis results
- **Redis**: Message broker for Celery tasks and caching
- **Backend**: FastAPI application providing the REST API
- **Celery Worker**: Processes background tasks like video analysis
- **Celery Beat**: Schedules periodic tasks
- **AI Analyzer**: Dedicated service for AI model inference
- **Frontend**: React application served by Nginx

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
