# Dependencies
/frontend/node_modules
/node_modules
*/node_modules
**/node_modules

# Build outputs
/frontend/dist
/frontend/build
/dist
/build

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
logs
*.log

# IDE and editor files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Test coverage
/coverage
/.nyc_output

# Temporary files
/tmp
/temp
.cache

# Test directories - explicitly ignore the entire tests directory and all its contents
tests/
tests/**/*
test/
test/**/*

# Playwright specific
test-results/
playwright-report/
playwright/.cache/
**/playwright/

# Test scripts - explicitly list all test files
test_*.js
test_*.ts
test_api.js
test_comprehensive.js
test_database.js
test_processing.js
test_spanish_transcript.js
test_tag_management.js
test_tag_management_ui.js
test_ui.js
test_video_analysis.js

# Large model files
*.bin
*.pt
models/
backend/models/
