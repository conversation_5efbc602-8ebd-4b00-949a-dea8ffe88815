FROM --platform=linux/arm64 debian:bullseye AS builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    git \
    && rm -rf /var/lib/apt/lists/*

# Clone and build whisper.cpp
WORKDIR /build
RUN git clone https://github.com/ggerganov/whisper.cpp.git . \
    && CFLAGS="-march=armv8-a" make server

# Create a clean runtime image
FROM --platform=linux/arm64 debian:bullseye-slim

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    libgomp1 \
    wget \
    && rm -rf /var/lib/apt/lists/*

# Copy the built server from the builder
COPY --from=builder /build/server /usr/local/bin/
COPY --from=builder /build/models/download-ggml-model.sh /usr/local/bin/

# Create directories for models and data
RUN mkdir -p /models /data

# Set working directory
WORKDIR /data

# Expose the server port
EXPOSE 9000

# Set the default command to run the server
CMD ["/usr/local/bin/server", "-m", "/models/ggml-base.bin", "-p", "9000", "-t", "8"] 