import os
import tempfile
import logging
from fastapi import FastAPI, UploadFile, File
from fastapi.responses import JSONResponse
import whisper
import threading

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("whisper-server")

app = FastAPI(title="Enhanced Whisper Transcription API",
              description="API for transcribing audio with support for multiple languages and models")

# Models dictionary to hold different Whisper models
models = {}

# Flag to indicate if we should load the medium model
LOAD_MEDIUM_MODEL = os.getenv("LOAD_MEDIUM_MODEL", "false").lower() == "true"
# Get device from environment variable (cuda or cpu)
DEVICE = os.getenv("DEVICE", "cpu")
# Check if CUDA is actually available, fallback to CPU if not
import torch
if DEVICE == "cuda" and not torch.cuda.is_available():
    logger.warning("CUDA requested but not available, falling back to CPU")
    DEVICE = "cpu"

# Set the Whisper models directory from environment variable
WHISPER_MODELS_DIR = os.getenv("WHISPER_MODELS_DIR", "/models")
logger.info(f"Using Whisper models directory: {WHISPER_MODELS_DIR}")

# Load the base Whisper model (will use pre-downloaded model if available)
try:
    logger.info(f"Loading base Whisper model from {WHISPER_MODELS_DIR} on {DEVICE}...")
    models["base"] = whisper.load_model("base", device=DEVICE, download_root=WHISPER_MODELS_DIR)
    logger.info(f"Successfully loaded base Whisper model on {DEVICE}")
except Exception as e:
    logger.error(f"Failed to load base model: {e}")
    models["base"] = None

# Function to load the medium model in a separate thread
def load_medium_model():
    try:
        logger.info(f"Loading medium Whisper model from {WHISPER_MODELS_DIR} on {DEVICE}...")
        medium_model = whisper.load_model("medium", device=DEVICE, download_root=WHISPER_MODELS_DIR)
        models["medium"] = medium_model
        logger.info(f"Successfully loaded medium Whisper model for Spanish on {DEVICE}")
    except Exception as e:
        logger.error(f"Failed to load medium model: {e}")
        models["medium"] = None

# Start loading the medium model in a background thread if enabled
if LOAD_MEDIUM_MODEL:
    medium_thread = threading.Thread(target=load_medium_model)
    medium_thread.daemon = True
    medium_thread.start()
    logger.info("Started background thread to load medium model")
else:
    logger.info("Medium model loading is disabled")

def optimize_options_for_language(language: str, options: dict) -> dict:
    """Optimize transcription options based on language."""
    if language == 'es':
        logger.info("Optimizing for Spanish transcription")
        # Use a higher temperature for Spanish to allow more exploration
        options["temperature"] = 0.3
        # Set initial prompt to help with Spanish recognition
        options["initial_prompt"] = "Este es un audio en español. La transcripción completa es la siguiente:"
        # Set a higher value for best_of to improve quality
        options["best_of"] = 5

        # Remove parameters that are not supported by Whisper
        if "beam_size" in options:
            del options["beam_size"]
        if "threads" in options:
            del options["threads"]
    elif language == 'en':
        logger.info("Optimizing for English transcription")
        options["temperature"] = 0.0  # More deterministic for English

        # Remove parameters that are not supported by Whisper
        if "beam_size" in options:
            del options["beam_size"]

    return options

def get_model_for_language(language: str):
    """Get the appropriate model based on language."""
    # For Spanish, try to use medium model if available
    if language == 'es' and 'medium' in models and models['medium'] is not None:
        logger.info("Using medium model for Spanish content")
        return models['medium']

    # Otherwise use base model
    logger.info(f"Using base model for {language} content")
    return models['base']

def post_process_spanish(text: str) -> str:
    """Apply post-processing to Spanish text to improve quality."""
    if not text:
        return text

    # Common corrections for Spanish transcriptions
    corrections = {
        "á": "á",  # Fix accented characters
        "é": "é",
        "í": "í",
        "ó": "ó",
        "ú": "ú",
        "ñ": "ñ",
        "¿": "¿",
        "¡": "¡",
    }

    for wrong, correct in corrections.items():
        text = text.replace(wrong, correct)

    # Fix common Spanish transcription errors
    common_errors = {
        "esta bien": "está bien",
        "asi": "así",
        "tambien": "también",
        "aqui": "aquí",
        "dia": "día",
        "dias": "días",
        "tu ": "tú ",
        "mas ": "más ",
        "si ": "sí ",
        "esta ": "está ",
        "como ": "cómo ",
        "que ": "qué ",
        "cuando ": "cuándo ",
        "donde ": "dónde ",
        "quien ": "quién ",
        "quienes ": "quiénes ",
        "por que": "por qué",
        "porque": "por qué",
        "estan": "están",
        "esta": "está",
        "ahi": "ahí",
        "cafe": "café",
        "facil": "fácil",
        "dificil": "difícil",
        "musica": "música",
        "pelicula": "película",
        "telefono": "teléfono",
        "numero": "número",
        "ultimo": "último",
        "rapido": "rápido",
        "proximo": "próximo",
        "jovenes": "jóvenes",
        "arbol": "árbol",
        "angel": "ángel",
        "album": "álbum",
        "examen": "exámen",
        "imagen": "imágen",
        "volumen": "volúmen",
        "joven": "jóven",
        "origen": "orígen",
        "margen": "márgen",
    }

    # Apply common error corrections
    for wrong, correct in common_errors.items():
        # Use word boundary to avoid replacing parts of words
        text = text.replace(f" {wrong} ", f" {correct} ")
        # Check at beginning of text
        if text.startswith(f"{wrong} "):
            text = f"{correct} " + text[len(wrong)+1:]
        # Check at end of text
        if text.endswith(f" {wrong}"):
            text = text[:-len(wrong)-1] + f" {correct}"

    # Fix punctuation
    text = text.replace(" ,", ",")
    text = text.replace(" .", ".")
    text = text.replace(" :", ":")
    text = text.replace(" ;", ";")
    text = text.replace(" ?", "?")
    text = text.replace(" !", "!")

    # Fix double spaces
    while "  " in text:
        text = text.replace("  ", " ")

    return text

@app.post("/asr")
async def transcribe_audio(
    audio_file: UploadFile = File(...),
    language: str = None,
    model_size: str = "auto",
    temperature: float = None,
    beam_size: int = None,
    initial_prompt: str = None,
):
    """
    Transcribe audio using Whisper.

    - language: Optional language code (e.g., 'en', 'es')
    - model_size: Model size to use ('base', 'medium', 'auto')
    - temperature: Temperature for sampling
    - beam_size: Beam size for beam search
    - initial_prompt: Initial prompt for the model
    """
    try:
        logger.info(f"Received transcription request: language={language}, model={model_size}, file={audio_file.filename}")

        # Read the file content
        file_content = await audio_file.read()

        # Create a temporary file to store the uploaded audio
        with tempfile.NamedTemporaryFile(delete=False, suffix=".mp3") as temp_file:
            temp_file.write(file_content)
            temp_file_path = temp_file.name

        # Determine which model to use
        selected_model = None

        if model_size == "auto":
            # Auto-select model based on language
            if language == 'es':
                # For Spanish, try to use medium model first
                if 'medium' in models and models['medium'] is not None:
                    selected_model = models['medium']
                    logger.info("Auto-selected medium model for Spanish content")
                else:
                    selected_model = models["base"]
                    logger.info("Medium model not available, using base model for Spanish content")
            else:
                selected_model = models["base"]
                logger.info(f"Auto-selected base model for {language or 'unknown'} content")
        elif model_size in models and models[model_size] is not None:
            selected_model = models[model_size]
            logger.info(f"Using requested {model_size} model")
        else:
            selected_model = models["base"]
            logger.info(f"Requested model {model_size} not available, using base model")

        if selected_model is None:
            logger.warning("No model is available for transcription yet, returning temporary response")

            # Return a temporary response based on the language
            if language == 'es':
                temp_transcript = "El modelo de transcripción aún se está cargando. Por favor, inténtelo de nuevo en unos minutos."
                detected_lang = "es"
            else:
                temp_transcript = "The transcription model is still loading. Please try again in a few minutes."
                detected_lang = "en"

            return JSONResponse(
                status_code=200,
                content={
                    "text": temp_transcript,
                    "language": detected_lang,
                    "segments": [],
                    "loading": True,
                    "message": "Models are still loading"
                }
            )

        # Prepare options
        options = {
            "task": "transcribe",
            "fp16": DEVICE == "cuda",  # Use FP16 for CUDA, FP32 for CPU
            # Note: device is set when loading the model, not in the options
        }

        # Add language if provided
        if language:
            options["language"] = language

        # Add other parameters if provided
        if temperature is not None:
            options["temperature"] = temperature
        # Whisper doesn't support beam_size parameter
        # if beam_size is not None:
        #     options["beam_size"] = beam_size
        if initial_prompt:
            options["initial_prompt"] = initial_prompt

        # Optimize options based on language
        if language:
            options = optimize_options_for_language(language, options)

        # Remove any unsupported parameters
        for param in ["beam_size", "best_of", "threads", "patience"]:
            if param in options:
                del options[param]

        # Transcribe the audio
        try:
            logger.info(f"Transcribing audio with options: {options}")
            result = selected_model.transcribe(temp_file_path, **options)

            # Extract transcript and language
            transcript = result["text"].strip()
            detected_language = result.get("language", "unknown")

            logger.info(f"First attempt: detected language={detected_language}, transcript length={len(transcript)}")

            # If transcript is empty or very short but we have a language hint, try again with different options
            if (not transcript or len(transcript) < 10) and language:
                logger.info(f"Short/empty transcript received. Trying again with different options for {language}")

                # Try with different options
                retry_options = {
                    "task": "transcribe",
                    "language": language,
                    "temperature": 0.3,  # Higher temperature for more exploration
                    "fp16": False,
                    # Remove unsupported parameters
                    # "beam_size": 5,
                    # "patience": 2.0,
                }

                # Try with a different model if available
                retry_model = selected_model
                if language == 'es' and model_size != 'medium' and 'medium' in models and models['medium'] is not None:
                    logger.info("Trying with medium model for Spanish")
                    retry_model = models['medium']

                retry_result = retry_model.transcribe(temp_file_path, **retry_options)
                retry_transcript = retry_result["text"].strip()

                if retry_transcript and (len(retry_transcript) > len(transcript)):
                    logger.info(f"Retry successful, got better transcript of length {len(retry_transcript)}")
                    transcript = retry_transcript
                    detected_language = language  # Force the language to what was hinted
                    result = retry_result  # Update the full result

            # For Spanish content, apply post-processing to improve quality
            if detected_language == 'es' or language == 'es':
                original_transcript = transcript
                transcript = post_process_spanish(transcript)
                # Update the transcript in the result
                result["text"] = transcript
                logger.info(f"Applied Spanish post-processing. Before: {len(original_transcript)} chars, After: {len(transcript)} chars")

            # Check if we have a valid transcript
            if not transcript or len(transcript) < 5:
                logger.warning(f"Empty or very short transcript for {language or 'unknown'} content")

                # Special handling for Spanish content
                if language == 'es' or detected_language == 'es':
                    error_message = "No se pudo obtener la transcripción completa de este video en español. " + \
                                   "El sistema ha detectado que el contenido está en español pero necesita más tiempo para procesarlo. " + \
                                   "Por favor, inténtelo de nuevo más tarde o contacte con soporte técnico si el problema persiste."
                    return JSONResponse(
                        status_code=422,  # Unprocessable Entity
                        content={
                            "error": error_message,
                            "language": "es",
                            "detected": True,
                            "transcription_failed": True
                        }
                    )
                else:
                    return JSONResponse(
                        status_code=422,  # Unprocessable Entity
                        content={
                            "error": "Could not transcribe the audio. The content may be too short, unclear, or in an unsupported language.",
                            "language": detected_language,
                            "transcription_failed": True
                        }
                    )

            # Log the final result
            logger.info(f"Final transcript length: {len(transcript)}")
            logger.info(f"Final language: {detected_language}")

            # Return the result
            return JSONResponse(
                status_code=200,
                content={
                    "text": transcript,
                    "language": detected_language,
                    "segments": result.get("segments", []),
                    "confidence": result.get("language_probability", 1.0)
                }
            )
        except Exception as e:
            logger.error(f"Transcription error: {str(e)}")

            # Special handling for Spanish content
            if language == 'es':
                error_message = "Error al procesar el audio en español. Por favor, inténtelo de nuevo o contacte con soporte técnico."
                return JSONResponse(
                    status_code=500,
                    content={
                        "error": error_message,
                        "language": "es",
                        "technical_error": str(e)
                    }
                )
            else:
                return JSONResponse(
                    status_code=500,
                    content={"error": f"Transcription error: {str(e)}"}
                )
        finally:
            # Clean up the temporary file
            try:
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                    logger.info(f"Cleaned up temporary file: {temp_file_path}")
            except Exception as e:
                logger.warning(f"Failed to clean up temporary file: {e}")

    except Exception as e:
        logger.error(f"Transcription request error: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"error": str(e)},
        )

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    # Always return healthy to prevent container restarts during model download
    # Check if at least the base model is loaded
    base_model_loaded = models.get("base") is not None

    return {
        "status": "ok",
        "models": list(models.keys()),
        "base_model_loaded": base_model_loaded,
        "medium_model_loading": LOAD_MEDIUM_MODEL and models.get("medium") is None,
        "loading": models.get("medium") is None if LOAD_MEDIUM_MODEL else models.get("base") is None,
        "message": "Models are still loading" if (LOAD_MEDIUM_MODEL and models.get("medium") is None) or models.get("base") is None else "All models loaded"
    }

@app.get("/models")
async def list_models():
    """List available models."""
    return {
        "models": list(models.keys())
    }

