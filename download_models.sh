#!/bin/bash

# <PERSON>ript to download Whisper models before starting the main services
# This ensures the models are already available when the Whisper services start

echo "Starting Whisper model download..."

# Create the model directories if they don't exist
mkdir -p ./models/whisper-base
mkdir -p ./models/whisper-medium

# Build the downloader image
docker build -t whisper-model-downloader -f Dockerfile.whisper-downloader .

# Download the base model
echo "Downloading base model..."
docker run --rm \
  -v "$(pwd)/models:/models" \
  whisper-model-downloader python download_whisper_models.py --models base --output-dir /models

# Download the medium model
echo "Downloading medium model..."
docker run --rm \
  -v "$(pwd)/models:/models" \
  whisper-model-downloader python download_whisper_models.py --models medium --output-dir /models

echo "Model download complete. Starting services..."

# Start the services
docker compose up -d

echo "All services started."
