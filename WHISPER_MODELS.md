# Whisper Models Pre-Download Guide

This document explains how to pre-download Whisper models to avoid having to download them every time the containers start.

## Why Pre-Download Models?

The Whisper speech-to-text service uses machine learning models that need to be downloaded before they can be used. These models are quite large:
- Base model: ~140MB
- Medium model: ~1.5GB

By default, these models are downloaded when the containers start, which can cause delays in processing videos, especially when the system is first started or restarted.

## How to Pre-Download Models

We've created a script that pre-downloads the models and stores them in a persistent volume. This way, the models are immediately available when the containers start.

### Option 1: Using the Download Script

1. Make sure the script is executable:
   ```bash
   chmod +x download_models.sh
   ```

2. Run the script:
   ```bash
   ./download_models.sh
   ```

This script will:
1. Build a Docker image for downloading the models
2. Download the base and medium models
3. Start the Docker Compose services

### Option 2: Manual Download

If you prefer to download the models manually:

1. Build the downloader image:
   ```bash
   docker build -t whisper-model-downloader -f Dockerfile.whisper-downloader .
   ```

2. Download the base model:
   ```bash
   docker run --rm -v "$(pwd)/models:/models" whisper-model-downloader python download_whisper_models.py --models base --output-dir /models
   ```

3. Download the medium model:
   ```bash
   docker run --rm -v "$(pwd)/models:/models" whisper-model-downloader python download_whisper_models.py --models medium --output-dir /models
   ```

4. Start the services:
   ```bash
   docker compose up -d
   ```

## Verifying the Models

To verify that the models have been downloaded correctly:

1. Check the models directory:
   ```bash
   ls -la ./models
   ```

2. You should see the model files in the directory.

3. Check the logs of the Whisper services to confirm they're using the pre-downloaded models:
   ```bash
   docker logs whisper-cpp
   docker logs whisper-cpp-spanish
   ```

   Look for messages like:
   ```
   Loading base Whisper model from /models on cpu...
   Successfully loaded base Whisper model on cpu
   ```

## Troubleshooting

If you encounter issues with the pre-downloaded models:

1. Delete the models directory and try downloading again:
   ```bash
   rm -rf ./models/*
   ./download_models.sh
   ```

2. Check the Docker logs for any error messages:
   ```bash
   docker logs whisper-model-downloader
   ```

3. Make sure you have enough disk space for the models (at least 2GB).

4. If the models are still not being used, check the Whisper server logs for any error messages related to loading the models.
