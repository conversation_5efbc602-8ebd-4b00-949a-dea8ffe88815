from fastapi import <PERSON>AP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, HttpUrl

app = FastAPI(title="TagTok Downloader", description="Service for downloading TikTok videos")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, replace with specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


class DownloadRequest(BaseModel):
    url: HttpUrl


class DownloadResponse(BaseModel):
    message: str
    video_id: int = None
    status: str


@app.post("/api/download", response_model=DownloadResponse)
async def download_video(download_request: DownloadRequest):
    """
    Mock endpoint for downloading a TikTok video from the provided URL.
    """
    return {
        "message": "Download started (mock)",
        "status": "pending"
    }


@app.get("/api/health")
def health_check():
    return {"status": "ok"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8081)
