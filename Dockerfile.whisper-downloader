FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    curl \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies with optimized versions
RUN pip install --no-cache-dir \
    openai-whisper==20231117 \
    torch==2.1.2 \
    numpy==1.26.3 \
    numba==0.58.1 \
    tqdm==4.66.1

# Create directories for models
RUN mkdir -p /models /app/cache

# Set environment variables for better performance
ENV OMP_NUM_THREADS=8
ENV MKL_NUM_THREADS=8
ENV NUMEXPR_NUM_THREADS=8
ENV WHISPER_MODELS_DIR=/models
ENV WHISPER_CACHE_DIR=/app/cache
ENV PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

# Copy the downloader script
COPY download_whisper_models.py /app/

WORKDIR /app

# Run the downloader script to pre-download models
CMD ["python", "download_whisper_models.py", "--models", "base", "medium", "--output-dir", "/models"]
