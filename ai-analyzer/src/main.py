import os
import json
import cv2
import numpy as np
import torch
from aiohttp import web
from transformers import CLIPProcessor, CLIPModel
import subprocess
import logging

# Try to import Llama, but don't fail if it's not available
try:
    from llama_cpp import Llama
    LLAMA_AVAILABLE = True
except ImportError:
    LLAMA_AVAILABLE = False
    logging.warning("llama-cpp-python not available. Topic generation will be limited.")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Environment variables
videos_dir = os.getenv("VIDEOS_DIR", "/videos")
models_dir = os.getenv("MODELS_DIR", "/models")
device = os.getenv("DEVICE", "cpu")  # 'cpu' or 'cuda'

# Global variables for models
clip_model = None
clip_processor = None
llama_model = None


async def load_models():
    """
    Load the AI models.
    """
    global clip_model, clip_processor, llama_model

    logger.info("Loading AI models...")

    # Load CLIP model
    if clip_model is None or clip_processor is None:
        try:
            logger.info("Loading CLIP model...")
            clip_model = CLIPModel.from_pretrained("openai/clip-vit-base-patch32")
            clip_processor = CLIPProcessor.from_pretrained("openai/clip-vit-base-patch32")

            # Move model to GPU if available
            if device == "cuda" and torch.cuda.is_available():
                clip_model = clip_model.to("cuda")
                logger.info("CLIP model loaded on GPU")
            else:
                logger.info("CLIP model loaded on CPU")
        except Exception as e:
            logger.error(f"Error loading CLIP model: {e}")
            raise

    # Load Llama model if available
    if LLAMA_AVAILABLE and llama_model is None:
        try:
            logger.info("Loading Llama model...")
            llama_model_path = os.path.join(models_dir, "llama-2-7b-chat.Q4_K_M.gguf")
            if os.path.exists(llama_model_path):
                llama_model = Llama(
                    model_path=llama_model_path,
                    n_ctx=2048,
                    n_gpu_layers=-1 if device == "cuda" and torch.cuda.is_available() else 0
                )
                logger.info("Llama model loaded")
            else:
                logger.warning(f"Llama model file not found at {llama_model_path}")
        except Exception as e:
            logger.error(f"Error loading Llama model: {e}")
            # Don't raise exception, continue without Llama

    logger.info("All models loaded successfully")


async def extract_frames(video_path, num_frames=8):
    """
    Extract evenly-spaced frames from the video.
    """
    frames = []

    # Open the video file
    cap = cv2.VideoCapture(video_path)

    # Get video properties
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    duration = frame_count / fps if fps > 0 else 0

    # Calculate frame indices to extract
    if frame_count <= num_frames:
        # If there are fewer than num_frames frames, use all of them
        frame_indices = list(range(frame_count))
    else:
        # Extract num_frames evenly-spaced frames
        frame_indices = [int(i * frame_count / num_frames) for i in range(num_frames)]

    # Extract the frames
    for idx in frame_indices:
        cap.set(cv2.CAP_PROP_POS_FRAMES, idx)
        ret, frame = cap.read()
        if ret:
            # Convert BGR to RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            frames.append(frame_rgb)

    # Release the video capture
    cap.release()

    return frames, duration


async def generate_frame_labels(frame):
    """
    Generate top-3 labels for a frame using the CLIP model.
    """
    # List of common objects and concepts for CLIP to classify
    candidate_labels = [
        "person", "dance", "singing", "talking", "outdoors", "indoors",
        "food", "animal", "pet", "music", "concert", "sports", "gaming",
        "makeup", "fashion", "comedy", "tutorial", "cooking", "fitness",
        "travel", "nature", "city", "beach", "party", "celebration",
        "challenge", "trend", "reaction", "review", "unboxing", "prank",
        "art", "craft", "DIY", "technology", "product", "advertisement",
        "educational", "informative", "news", "politics", "entertainment"
    ]

    # Process the image with CLIP
    inputs = clip_processor(
        text=candidate_labels,
        images=frame,
        return_tensors="pt",
        padding=True
    )

    # Move inputs to the same device as the model
    if device == "cuda" and torch.cuda.is_available():
        inputs = {k: v.to("cuda") for k, v in inputs.items()}

    # Get the model's predictions
    with torch.no_grad():
        outputs = clip_model(**inputs)
        logits_per_image = outputs.logits_per_image
        probs = logits_per_image.softmax(dim=1)

    # Get the top 3 predictions
    top_probs, top_indices = probs[0].topk(3)

    # Convert to list of (label, probability) tuples
    top_labels = [
        {
            "label": candidate_labels[idx],
            "probability": float(prob)
        }
        for prob, idx in zip(top_probs.cpu().numpy(), top_indices.cpu().numpy())
    ]

    return top_labels


async def extract_transcript(video_path):
    """
    Extract transcript from the video using whisper-cpp.
    """
    try:
        # Run whisper-cpp command to extract transcript
        whisper_cmd = [
            "whisper-cpp",
            "-m", "/models/ggml-base.bin",  # Path to the multilingual whisper model
            "-f", video_path,                  # Input video file
            "-otxt"                            # Output format (text)
        ]

        # Execute the command
        result = subprocess.run(
            whisper_cmd,
            capture_output=True,
            text=True,
            check=True
        )

        # Get the output transcript file path
        transcript_file = os.path.splitext(video_path)[0] + ".txt"

        # Read the transcript file if it exists
        if os.path.exists(transcript_file):
            with open(transcript_file, "r") as f:
                transcript = f.read().strip()

            # Clean up the transcript file
            os.remove(transcript_file)

            return transcript

        # If the file doesn't exist, return the stdout from the command
        return result.stdout.strip()

    except subprocess.CalledProcessError as e:
        logger.error(f"Error extracting transcript: {e}")
        logger.error(f"stderr: {e.stderr}")
        return ""
    except Exception as e:
        logger.error(f"Error extracting transcript: {e}")
        return ""


async def generate_summary_paragraph(frame_labels, transcript):
    """
    Generate a summary paragraph from frame labels and transcript.
    """
    # Extract unique labels from all frames
    all_labels = set()
    for frame in frame_labels:
        for label_info in frame["labels"]:
            all_labels.add(label_info["label"])

    # Create a summary paragraph
    summary = "This video contains: " + ", ".join(all_labels)

    # Add transcript summary if available
    if transcript:
        # Truncate transcript if it's too long
        max_transcript_length = 500
        transcript_summary = transcript[:max_transcript_length]
        if len(transcript) > max_transcript_length:
            transcript_summary += "..."

        summary += f"\n\nTranscript: {transcript_summary}"

    return summary


async def generate_topic_and_keywords(summary_paragraph):
    """
    Generate the main topic and keywords using the Llama model if available,
    otherwise use a simple rule-based approach.
    """
    # If Llama is not available or model is not loaded, use a simple approach
    if not LLAMA_AVAILABLE or llama_model is None:
        logger.info("Using simple topic and keyword generation (Llama model not available)")
        # Extract potential keywords from the summary
        words = summary_paragraph.lower().replace('\n', ' ').split()
        # Remove common words and punctuation
        filtered_words = [w.strip('.,!?:;()"') for w in words if len(w) > 3]
        # Count word frequency
        word_counts = {}
        for word in filtered_words:
            if word in word_counts:
                word_counts[word] += 1
            else:
                word_counts[word] = 1

        # Sort by frequency
        sorted_words = sorted(word_counts.items(), key=lambda x: x[1], reverse=True)

        # Get top 5 keywords
        keywords = [word for word, count in sorted_words[:5]]

        # Ensure we have exactly 5 keywords
        while len(keywords) < 5:
            keywords.append(f"keyword{len(keywords)+1}")

        # Generate a simple topic
        if keywords:
            topic = f"Video about {keywords[0]}"
        else:
            topic = "Unknown topic"

        return topic, keywords

    # Use Llama model if available
    # Create the prompt for the Llama model
    prompt = f"""
You are an AI assistant that analyzes TikTok videos.
Based on the following information about a video, summarize the main topic in a short phrase (max 10 words) and provide 5 comma-separated keywords.

Information about the video:
{summary_paragraph}

Format your response exactly like this:
Topic: [main topic]
Keywords: [keyword1], [keyword2], [keyword3], [keyword4], [keyword5]
"""

    # Generate response from the Llama model
    try:
        response = llama_model(
            prompt,
            max_tokens=256,
            stop=["</s>"],
            temperature=0.7
        )

        # Extract the generated text
        generated_text = response["choices"][0]["text"]

        # Parse the topic and keywords from the response
        topic = ""
        keywords = []

        for line in generated_text.strip().split("\n"):
            if line.startswith("Topic:"):
                topic = line.replace("Topic:", "").strip()
            elif line.startswith("Keywords:"):
                keywords_text = line.replace("Keywords:", "").strip()
                keywords = [k.strip() for k in keywords_text.split(",") if k.strip()]

        # Ensure we have exactly 5 keywords
        while len(keywords) < 5:
            keywords.append(f"keyword{len(keywords)+1}")

        # Limit to 5 keywords if we have more
        keywords = keywords[:5]

        return topic, keywords

    except Exception as e:
        logger.error(f"Error generating topic and keywords: {e}")
        return "Unknown topic", ["keyword1", "keyword2", "keyword3", "keyword4", "keyword5"]


async def analyze_video_handler(request):
    """
    Handle requests to analyze a video.
    """
    try:
        # Parse the request body
        data = await request.json()
        video_path = data.get("video_path")

        if not video_path:
            return web.json_response(
                {"error": "Missing video_path parameter"},
                status=400
            )

        # Get the full path to the video file
        full_video_path = os.path.join(videos_dir, video_path)

        if not os.path.exists(full_video_path):
            return web.json_response(
                {"error": f"Video file not found: {full_video_path}"},
                status=404
            )

        # Extract frames from the video
        frames, duration = await extract_frames(full_video_path)

        if not frames:
            return web.json_response(
                {"error": "Failed to extract frames from the video"},
                status=500
            )

        # Generate labels for each frame
        frame_labels = []
        for i, frame in enumerate(frames):
            labels = await generate_frame_labels(frame)
            frame_labels.append({
                "frame_index": i,
                "timestamp": i * (duration / len(frames)) if duration else 0,
                "labels": labels
            })

        # Extract transcript using whisper-cpp
        transcript = await extract_transcript(full_video_path)

        # Generate a summary paragraph from frame labels and transcript
        summary_paragraph = await generate_summary_paragraph(frame_labels, transcript)

        # Generate topic and keywords using Llama model
        topic, keywords = await generate_topic_and_keywords(summary_paragraph)

        # Return the analysis results
        return web.json_response({
            "video_path": video_path,
            "duration": duration,
            "frame_labels": frame_labels,
            "transcript": transcript,
            "topic": topic,
            "keywords": keywords
        })

    except Exception as e:
        logger.error(f"Error analyzing video: {e}")
        return web.json_response(
            {"error": str(e)},
            status=500
        )


async def health_check_handler(request):
    """
    Handle health check requests.
    """
    return web.json_response({"status": "ok"})


async def on_startup(app):
    """
    Initialize the application on startup.
    """
    try:
        await load_models()
    except Exception as e:
        logger.error(f"Error loading models: {e}")
        logger.warning("Continuing with limited functionality")


def create_app():
    """
    Create and configure the aiohttp application.
    """
    app = web.Application()

    # Register routes
    app.router.add_post("/analyze", analyze_video_handler)
    app.router.add_get("/health", health_check_handler)

    # Register startup handler
    app.on_startup.append(on_startup)

    return app


if __name__ == "__main__":
    app = create_app()
    web.run_app(app, host="0.0.0.0", port=8080)
