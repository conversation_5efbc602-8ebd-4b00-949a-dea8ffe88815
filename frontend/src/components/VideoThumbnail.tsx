import React from 'react';
import { format } from 'date-fns';
import { Video, Tag } from '../api/videos';
import { TrashIcon } from '@heroicons/react/24/outline';

interface VideoThumbnailProps {
  video: Video;
  onClick: () => void;
  onDelete: (videoId: number) => void;
}

const VideoThumbnail: React.FC<VideoThumbnailProps> = ({ video, onClick, onDelete }) => {
  // Format the duration (seconds) to MM:SS
  const formatDuration = (seconds?: number) => {
    if (!seconds) return '00:00';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // Generate a thumbnail URL
  const thumbnailUrl = `/api/videos/${video.id}/thumbnail`;
  
  // Fallback to a placeholder if no thumbnail is available
  const handleThumbnailError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    e.currentTarget.src = 'https://via.placeholder.com/320x180?text=Error';
  };

  const handleDeleteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (window.confirm(`Are you sure you want to delete "${video.title}"?`)) {
      onDelete(video.id);
    }
  };

  return (
    <div 
      className="relative group bg-gray-800 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow"
    >
      <button
        onClick={handleDeleteClick}
        className="absolute top-2 right-2 z-20 p-1.5 bg-black bg-opacity-50 rounded-full text-white hover:bg-red-600 hover:bg-opacity-75 opacity-0 group-hover:opacity-100 transition-all duration-200"
        aria-label="Delete video"
      >
        <TrashIcon className="h-5 w-5" />
      </button>

      <div onClick={onClick} className="cursor-pointer">
        <div className="relative">
          <img
            src={thumbnailUrl}
            alt={video.title}
            className="w-full h-48 object-cover block"
            onError={handleThumbnailError}
            loading="lazy"
          />
          <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded z-10">
            {formatDuration(video.duration)}
          </div>
          <div className="absolute inset-0 p-3 bg-gradient-to-t from-black via-black/70 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end z-10">
            {video.tags && video.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-1">
                {video.tags.slice(0, 5).map((tag: Tag) => (
                  <span
                    key={tag.id}
                    className="bg-blue-600 bg-opacity-90 text-white text-xs px-1.5 py-0.5 rounded"
                  >
                    {tag.name}
                  </span>
                ))}
                {video.tags.length > 5 && (
                  <span className="bg-gray-600 bg-opacity-90 text-white text-xs px-1.5 py-0.5 rounded">
                    +{video.tags.length - 5}
                  </span>
                )}
              </div>
            )}
          </div>
        </div>

        <div className="p-3">
          <h3 className="text-white font-semibold text-md truncate">{video.title}</h3>
          <p className="text-gray-400 text-sm">
            {video.tiktok_author && `@${video.tiktok_author} • `}
            {format(new Date(video.download_date), 'MMM d, yyyy')}
          </p>
          {video.main_topic && (
            <p className="text-gray-300 text-xs mt-1 truncate">Topic: {video.main_topic}</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default VideoThumbnail;
