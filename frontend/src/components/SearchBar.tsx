import React, { useState } from 'react';
import { MagnifyingGlassIcon } from '@heroicons/react/24/outline';

interface SearchBarProps {
  onSearch: (query: string) => void;
}

const SearchBar: React.FC<SearchBarProps> = ({ onSearch }) => {
  const [query, setQuery] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSearch(query);
  };

  return (
    <form onSubmit={handleSubmit} className="w-full max-w-lg">
      <div className="relative">
        <input
          type="text"
          id="search-input"
          name="search-input"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Search videos by title..."
          className="w-full bg-gray-700 text-white pl-10 pr-12 py-2 rounded-md border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
          aria-label="Search videos"
        />
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
        </div>
        <button
          type="submit"
          aria-label="Submit search"
          className="absolute inset-y-0 right-0 px-3 flex items-center text-gray-400 hover:text-white bg-gray-600 rounded-r-md"
        >
          <span className="text-sm">Search</span>
        </button>
      </div>
    </form>
  );
};

export default SearchBar;
