/// <reference types="react" />
import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Select, { components, OptionProps, GroupBase, MultiValue } from 'react-select';
import { getTags, deleteTag } from '../api/tags';
import { Tag } from '../api/videos';
import { FaTimes } from 'react-icons/fa';

interface SidebarProps {
  selectedTags: Tag[];
  setSelectedTags: (tags: Tag[]) => void;
  startDate: string | null;
  setStartDate: (date: string | null) => void;
  endDate: string | null;
  setEndDate: (date: string | null) => void;
}

interface TagOption extends Tag {
  value: number;
  label: string;
}

const CustomOption: React.FC<OptionProps<TagOption, true, GroupBase<TagOption>>> = (props) => {
  const queryClient = useQueryClient();
  const deleteTagMutation = useMutation(deleteTag, {
    onSuccess: () => {
      queryClient.invalidateQueries(['tags']);
    },
  });

  const handleDelete = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    if (window.confirm('Are you sure you want to delete this tag?')) {
      deleteTagMutation.mutate(props.data.id);
    }
  };

  return (
    <components.Option {...props}>
      <span>{props.data.label}</span>
      <button
        className="ml-2 text-red-400 hover:text-red-600 p-1"
        onClick={handleDelete}
        disabled={deleteTagMutation.isLoading}
        title="Delete tag"
        style={{ background: 'none', border: 'none', cursor: 'pointer' }}
      >
        <FaTimes size={12} />
      </button>
    </components.Option>
  );
};

const Sidebar: React.FC<SidebarProps> = ({
  selectedTags,
  setSelectedTags,
  startDate,
  setStartDate,
  endDate,
  setEndDate,
}: SidebarProps) => {
  const { data: tags = [], isLoading } = useQuery<Tag[]>(['tags'], () => getTags());

  const handleTagChange = (selected: MultiValue<TagOption>) => {
    setSelectedTags(selected ? selected.map(tag => ({ id: tag.id, name: tag.name, description: tag.description, is_ai_generated: tag.is_ai_generated, created_at: tag.created_at, updated_at: tag.updated_at })) : []);
  };

  return (
    <div className="w-64 bg-gray-800 p-4 flex flex-col h-full">
      <h2 className="text-xl font-bold mb-4 text-white">Filters</h2>

      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Tags
        </label>
        <Select
          isMulti
          isLoading={isLoading}
          options={tags.map((tag) => ({ value: tag.id, label: tag.name, ...tag }))}
          value={selectedTags.map((tag) => ({ value: tag.id, label: tag.name, ...tag }))}
          onChange={handleTagChange}
          placeholder="Select tags..."
          className="react-select-container"
          classNamePrefix="react-select"
          components={{ Option: CustomOption }}
          styles={{
            control: (base: any) => ({
              ...base,
              backgroundColor: '#374151',
              borderColor: '#4B5563',
            }),
            menu: (base: any) => ({
              ...base,
              backgroundColor: '#374151',
            }),
            option: (base: any, state: any) => ({
              ...base,
              backgroundColor: state.isFocused ? '#4B5563' : '#374151',
              color: 'white',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
            }),
            multiValue: (base: any) => ({
              ...base,
              backgroundColor: '#4B5563',
            }),
            multiValueLabel: (base: any) => ({
              ...base,
              color: 'white',
            }),
            input: (base: any) => ({
              ...base,
              color: 'white',
            }),
            singleValue: (base: any) => ({
              ...base,
              color: 'white',
            }),
          }}
        />
      </div>

      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-300 mb-2">
          Date Range
        </label>
        <div className="space-y-2">
          <div>
            <label className="block text-xs text-gray-400 mb-1">Start Date</label>
            <input
              type="date"
              value={startDate || ''}
              onChange={(e) => setStartDate(e.target.value || null)}
              className="w-full bg-gray-700 text-white px-3 py-2 rounded-md border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div>
            <label className="block text-xs text-gray-400 mb-1">End Date</label>
            <input
              type="date"
              value={endDate || ''}
              onChange={(e) => setEndDate(e.target.value || null)}
              className="w-full bg-gray-700 text-white px-3 py-2 rounded-md border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      <button
        onClick={() => {
          setSelectedTags([]);
          setStartDate(null);
          setEndDate(null);
        }}
        className="mt-auto bg-gray-700 hover:bg-gray-600 text-white py-2 px-4 rounded-md transition-colors"
      >
        Clear Filters
      </button>
    </div>
  );
};

export default Sidebar;
