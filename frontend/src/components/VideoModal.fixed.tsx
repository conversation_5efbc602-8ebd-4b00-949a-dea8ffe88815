import React, { useState, useEffect } from 'react';
import { Dialog } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import Select from 'react-select';
import toast from 'react-hot-toast';
import { useQueryClient } from '@tanstack/react-query';
import { useNotifications } from '../context/NotificationContext';
import { getVideo, reanalyzeVideo } from '../api/videos';

// Define the VideoModal component
const VideoModal = ({ 
  isOpen, 
  onClose, 
  video, 
  allTags 
}) => {
  const [newTagName, setNewTagName] = useState('');
  const [analyzing, setAnalyzing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [jobStatus, setJobStatus] = useState(null);
  const [notificationId, setNotificationId] = useState(null);
  
  const queryClient = useQueryClient();
  const { addNotification, updateNotification } = useNotifications();

  // Define progress thresholds for long-running jobs
  const PROGRESS_THRESHOLDS = {
    FIVE_MIN: 300000,    // 5 minutes in ms
    TEN_MIN: 600000,     // 10 minutes in ms
    FIFTEEN_MIN: 900000, // 15 minutes in ms
    TWENTY_MIN: 1200000, // 20 minutes in ms
    THIRTY_MIN: 1800000  // 30 minutes in ms - auto-fail threshold
  };

  // Start and end progress values for each status
  const progressRanges = {
    PENDING: { start: 0.05, end: 0.4 },
    PROCESSING: { start: 0.4, end: 0.98 },
    COMPLETED: { start: 0.98, end: 1 },
    FAILED: { start: 0.98, end: 1 },
    // Add a default case to handle unknown statuses
    unknown: { start: 0.05, end: 0.7 }
  };

  // Function to get the latest analysis job for a video
  const getLatestAnalysisJob = async (videoId) => {
    try {
      const response = await fetch(`/api/analysis-jobs/video/${videoId}`);
      const data = await response.json();
      return data && data.length > 0 ? data[0] : null;
    } catch (e) {
      console.error('Error fetching job status:', e);
      return null;
    }
  };

  // Function to poll the job status
  const pollJobStatus = async (videoId) => {
    // Constants for polling
    const LOG_INTERVAL = 10000; // 10 seconds between log messages
    const maxAttempts = 300; // Maximum number of polling attempts (5 minutes at 1s intervals)
    
    // Variables for tracking state
    let attempts = 0;
    let polling = true;
    let lastLogTime = Date.now();
    let lastStatus = null;
    let statusStartTime = Date.now();
    let startTime = Date.now();
    let errorCount = 0;
    let savedProgress = 0;
    
    // Storage key for saving progress
    const storageKey = `analysis_job_${videoId}`;
    
    // Try to restore state from localStorage
    let savedStateObj = null;
    try {
      const savedState = localStorage.getItem(storageKey);
      if (savedState) {
        savedStateObj = JSON.parse(savedState);
        // Only restore state if it's for the same video
        if (savedStateObj.videoId === videoId) {
          attempts = savedStateObj.attempts || 0;
          startTime = savedStateObj.startTime || Date.now();
          lastStatus = savedStateObj.lastStatus || null;
          statusStartTime = savedStateObj.statusStartTime || startTime;
          savedProgress = savedStateObj.progress || 0;
          
          // Update the UI with the saved progress immediately
          setProgress(savedProgress);
          // Only log important state restorations
          console.log(`Restored analysis job state for video ${videoId}, progress: ${Math.round(savedProgress * 100)}%`);
        }
      }
    } catch (e) {
      console.error('Error parsing saved analysis state:', e);
    }
    
    // Log start of polling only once
    console.log(`Starting to poll job status for video ${videoId}`);
    
    try {
      while (polling && attempts < maxAttempts) {
        attempts++;
        try {
          const job = await getLatestAnalysisJob(videoId);
          
          // Only log status changes or at regular intervals to reduce console spam
          const currentTime = Date.now();
          const shouldLog = (job?.status !== lastStatus) ||
                          (currentTime - lastLogTime > LOG_INTERVAL);
          
          if (shouldLog) {
            console.log(`Poll attempt ${attempts}: Job status = ${job?.status || 'unknown'}`);
            lastLogTime = currentTime;
          }
          
          // Reset error counter on successful API call
          errorCount = 0;
          
          if (job) {
            // Check if the job has been running for too long (more than 30 minutes)
            const totalElapsedTime = Date.now() - startTime;
            
            // Auto-fail jobs that have been running for too long (30 minutes)
            if (totalElapsedTime > PROGRESS_THRESHOLDS.THIRTY_MIN) {
              console.log(`Job has been running for ${Math.round(totalElapsedTime/60000)} minutes, auto-failing due to timeout`);
              
              // Mark the job as failed
              setJobStatus('FAILED');
              setProgress(1.0); // Set progress to 100%
              
              // Clean up localStorage
              localStorage.removeItem(storageKey);
              
              // Update the notification
              if (notificationId) {
                updateNotification(notificationId, {
                  title: 'Video Analysis Timeout',
                  message: `Analysis timed out for: ${video?.title}. Please try again.`,
                  type: 'error',
                  progress: 1.0,
                  status: 'failed'
                });
              }
              
              // Show error toast
              toast.error('Analysis timed out. Please try again.', {
                id: 'analysis-timeout',
                duration: 4000,
                icon: '⏱️'
              });
              
              // Stop polling
              polling = false;
              break;
            }
            
            // Rest of the job handling code...
            // (This would be too long to include here, but would handle job status updates,
            // progress calculations, notifications, etc.)
            
          } else {
            // No job found, handle accordingly...
            // (This would update progress based on time, update notifications, etc.)
          }
        } catch (e) {
          // Handle errors in the polling loop...
          errorCount++;
          console.error('Error polling job status:', e);
        }
        
        // Wait before the next poll
        const pollInterval = 1000; // 1 second
        await new Promise(res => setTimeout(res, pollInterval));
      }
    } catch (e) {
      console.error('Unhandled error in polling loop:', e);
    }
    
    // Handle max attempts reached...
    if (attempts >= maxAttempts) {
      console.log('Max polling attempts reached, stopping');
      setAnalyzing(false);
      setProgress(0);
      setJobStatus('FAILED');
      
      // Show timeout notification...
    }
  };
  
  // Handler for re-analyzing a video
  const handleReanalyze = async () => {
    // Implementation of re-analyze functionality...
  };
  
  // Render the component
  if (!video) return null;
  
  return (
    <Dialog
      open={isOpen}
      onClose={onClose}
      className="relative z-50"
    >
      {/* Dialog content would go here */}
    </Dialog>
  );
};

export default VideoModal;
