import React, { useState, useRef, useEffect } from 'react';
import { BellIcon, CheckIcon, XMarkIcon, TrashIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { useNotifications, Notification } from '../context/NotificationContext';
import { checkStuckJobs } from '../api/videos';
import toast from 'react-hot-toast';

const NotificationBell: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAllNotifications
  } = useNotifications();
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleMarkAsRead = (id: string, e: React.MouseEvent) => {
    e.stopPropagation();
    markAsRead(id);
  };

  const handleRemove = (id: string, e: React.MouseEvent) => {
    e.stopPropagation();
    removeNotification(id);
  };

  const handleCheckStuckJobs = async (e: React.MouseEvent) => {
    e.stopPropagation();

    // Show loading toast
    toast.loading('Checking for stuck jobs...', { id: 'check-stuck-jobs' });

    try {
      const result = await checkStuckJobs();

      // Show success toast
      toast.success(`Check complete: ${result.message}`, { id: 'check-stuck-jobs' });

      // If any jobs were fixed, add a notification
      if (result.message.includes('found and fixed') && !result.message.includes('found and fixed 0')) {
        // Add a notification about the fixed jobs
        const fixedCount = parseInt(result.message.split('found and fixed ')[1]);
        if (fixedCount > 0) {
          // Add a notification
          const notification = {
            title: 'Stuck Jobs Fixed',
            message: `Fixed ${fixedCount} stuck analysis jobs. You may need to retry analysis for affected videos.`,
            type: 'warning' as const,
          };
          // We don't have access to addNotification here, but we can show a toast
          toast.success(`Fixed ${fixedCount} stuck analysis jobs`, {
            duration: 5000,
            icon: '🔧'
          });
        }
      }
    } catch (error) {
      console.error('Error checking for stuck jobs:', error);
      toast.error('Failed to check for stuck jobs', { id: 'check-stuck-jobs' });
    }
  };

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'success':
        return <div className="w-2 h-2 bg-green-500 rounded-full"></div>;
      case 'error':
        return <div className="w-2 h-2 bg-red-500 rounded-full"></div>;
      case 'warning':
        return <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>;
      case 'process':
        return <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>;
      default:
        return <div className="w-2 h-2 bg-gray-500 rounded-full"></div>;
    }
  };

  const formatTimestamp = (date: Date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={toggleDropdown}
        className="relative p-1 text-gray-400 hover:text-white focus:outline-none"
        aria-label="Notifications"
      >
        <BellIcon className="h-6 w-6" />
        {unreadCount > 0 && (
          <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-600 rounded-full">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-gray-800 rounded-md shadow-lg z-50 max-h-[80vh] overflow-hidden flex flex-col">
          <div className="p-3 border-b border-gray-700 flex justify-between items-center">
            <h3 className="text-white font-medium">Notifications</h3>
            <div className="flex space-x-2">
              {unreadCount > 0 && (
                <button
                  onClick={markAllAsRead}
                  className="text-xs bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded flex items-center"
                >
                  <CheckIcon className="h-3 w-3 mr-1" />
                  Mark all read
                </button>
              )}
              {notifications.length > 0 && (
                <button
                  onClick={clearAllNotifications}
                  className="text-xs bg-gray-700 hover:bg-gray-600 text-white px-2 py-1 rounded flex items-center"
                >
                  <TrashIcon className="h-3 w-3 mr-1" />
                  Clear all
                </button>
              )}
            </div>
          </div>

          {/* Add button to check for stuck jobs */}
          <div className="p-2 border-b border-gray-700 flex justify-center">
            <button
              onClick={handleCheckStuckJobs}
              className="text-xs bg-yellow-600 hover:bg-yellow-700 text-white px-2 py-1 rounded flex items-center"
            >
              <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
              Check for stuck jobs
            </button>
          </div>

          <div className="overflow-y-auto max-h-[calc(80vh-80px)]">
            {notifications.length === 0 ? (
              <div className="p-4 text-center text-gray-400">
                No notifications
              </div>
            ) : (
              <div>
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-3 border-b border-gray-700 hover:bg-gray-700 transition-colors ${notification.read ? 'opacity-70' : ''}`}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex items-start space-x-2">
                        <div className="mt-1">{getNotificationIcon(notification.type)}</div>
                        <div>
                          <h4 className="text-sm font-medium text-white">{notification.title}</h4>
                          <p className="text-xs text-gray-400">{notification.message}</p>
                          <p className="text-xs text-gray-500 mt-1">{formatTimestamp(notification.timestamp)}</p>

                          {notification.type === 'process' && notification.progress !== undefined && (
                            <div className="mt-2 w-full">
                              <div className="w-full bg-gray-600 rounded-full h-1.5">
                                <div
                                  className={`h-1.5 rounded-full transition-all duration-300 ${
                                    notification.status === 'failed' ? 'bg-red-600' :
                                    notification.status === 'completed' ? 'bg-green-500' : 'bg-blue-500'
                                  }`}
                                  style={{ width: `${notification.progress * 100}%` }}
                                ></div>
                              </div>
                              <div className="flex justify-between mt-1">
                                <span className="text-xs text-gray-400">
                                  {notification.status === 'pending' && 'Queued'}
                                  {notification.status === 'processing' && 'Processing'}
                                  {notification.status === 'completed' && 'Completed'}
                                  {notification.status === 'failed' && 'Failed'}
                                </span>
                                <span className="text-xs text-gray-400">{Math.round(notification.progress * 100)}%</span>
                              </div>

                              {/* Add retry button for failed jobs */}
                              {notification.status === 'failed' && notification.videoId && (
                                <button
                                  onClick={() => {
                                    if (notification.onAction) {
                                      notification.onAction();
                                    }
                                  }}
                                  className="mt-2 text-xs bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded"
                                >
                                  Retry Analysis
                                </button>
                              )}
                            </div>
                          )}

                          {notification.onAction && notification.actionText && (
                            <button
                              onClick={notification.onAction}
                              className="mt-2 text-xs bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded"
                            >
                              {notification.actionText}
                            </button>
                          )}
                        </div>
                      </div>
                      <div className="flex space-x-1">
                        {!notification.read && (
                          <button
                            onClick={(e) => handleMarkAsRead(notification.id, e)}
                            className="text-gray-400 hover:text-white"
                            title="Mark as read"
                          >
                            <CheckIcon className="h-4 w-4" />
                          </button>
                        )}
                        <button
                          onClick={(e) => handleRemove(notification.id, e)}
                          className="text-gray-400 hover:text-white"
                          title="Remove"
                        >
                          <XMarkIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default NotificationBell;
