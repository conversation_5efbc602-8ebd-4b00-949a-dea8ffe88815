import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getVideos, Video, VideoFilter, deleteVideo } from '../api/videos';
import VideoThumbnail from './VideoThumbnail';

interface VideoGridProps {
  filters: VideoFilter;
  onVideoClick: (video: Video) => void;
}

const VideoGrid: React.FC<VideoGridProps> = ({ filters, onVideoClick }) => {
  const queryClient = useQueryClient();
  
  const { data, isLoading, error } = useQuery(
    ['videos', filters],
    () => getVideos(filters),
    {
      keepPreviousData: true,
    }
  );
  
  const videos = data || [];

  const deleteMutation = useMutation(deleteVideo, {
    onSuccess: () => {
      queryClient.invalidateQueries(['videos', filters]);
      console.log('Video deleted successfully');
    },
    onError: (error) => {
      console.error('Error deleting video:', error);
    },
  });

  const handleDeleteVideo = (videoId: number) => {
    if (window.confirm('Are you sure you want to delete this video?')) {
        deleteMutation.mutate(videoId);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-red-500">Error loading videos. Please try again.</div>
      </div>
    );
  }

  if (!isLoading && videos.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-gray-500">No videos found. Try adjusting your filters.</div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-4">
      {videos.map((video) => (
        <VideoThumbnail
          key={video.id}
          video={video}
          onClick={() => onVideoClick(video)}
          onDelete={handleDeleteVideo}
        />
      ))}
    </div>
  );
};

export default VideoGrid;
