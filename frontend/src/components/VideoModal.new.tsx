import React from 'react';
import { Dialog } from '@headlessui/react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import toast from 'react-hot-toast';
import Select from 'react-select';
import { useNotifications } from '../context/NotificationContext';
import { Video, addTagToVideo, removeTagFromVideo, triggerAnalysis, getLatestAnalysisJob, getVideo } from '../api/videos';
import { getTags, createTag } from '../api/tags';

interface VideoModalProps {
  video: Video | null;
  isOpen: boolean;
  onClose: () => void;
}

const VideoModal: React.FC<VideoModalProps> = ({ video, isOpen, onClose }: VideoModalProps) => {
  const queryClient = useQueryClient();
  const { addNotification, updateNotification, notifications } = useNotifications();
  const [newTagName, setNewTagName] = React.useState('');
  const [analyzing, setAnalyzing] = React.useState(false);
  const [progress, setProgress] = React.useState(0);
  const [jobStatus, setJobStatus] = React.useState<string | null>(null);
  const [notificationId, setNotificationId] = React.useState<string | null>(null);

  // Define progress thresholds for long-running jobs
  const PROGRESS_THRESHOLDS = {
    FIVE_MIN: 300000,    // 5 minutes in ms
    TEN_MIN: 600000,     // 10 minutes in ms
    FIFTEEN_MIN: 900000, // 15 minutes in ms
    TWENTY_MIN: 1200000, // 20 minutes in ms
    THIRTY_MIN: 1800000  // 30 minutes in ms - auto-fail threshold
  };

  // Start and end progress values for each status
  const progressRanges: Record<string, { start: number; end: number }> = {
    PENDING: { start: 0.05, end: 0.4 },
    PROCESSING: { start: 0.4, end: 0.98 },
    COMPLETED: { start: 0.98, end: 1 },
    FAILED: { start: 0.98, end: 1 },
    // Add a default case to handle unknown statuses
    unknown: { start: 0.05, end: 0.7 }
  };

  // Check for existing analysis notification for this video when the modal opens
  React.useEffect(() => {
    if (video && isOpen) {
      // Check localStorage first for active analysis job
      const storageKey = `analysis_job_${video.id}`;
      const savedState = localStorage.getItem(storageKey);
      let hasActiveJob = false;

      if (savedState) {
        try {
          const state = JSON.parse(savedState);
          // Check if the saved state is recent (within the last 5 minutes)
          const isRecent = (Date.now() - (state.lastUpdated || 0)) < 5 * 60 * 1000;

          if (isRecent) {
            console.log(`Found active analysis job in localStorage for video ${video.id}`);
            setAnalyzing(true);
            setProgress(state.progress || 0);
            setJobStatus(state.lastStatus || 'PENDING');
            hasActiveJob = true;

            // Find the notification for this job
            const existingNotification = notifications
              .filter(n => n.videoId === video.id && n.type === 'process')
              .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())[0];

            if (existingNotification) {
              setNotificationId(existingNotification.id);
            }

            // Resume polling for job status
            pollJobStatus(video.id);
          } else {
            // Clean up old state
            localStorage.removeItem(storageKey);
          }
        } catch (e) {
          console.error('Error parsing saved analysis state:', e);
        }
      }

      // If no active job found in localStorage, check notifications
      if (!hasActiveJob) {
        // Find the most recent analysis notification for this video
        const existingNotification = notifications
          .filter(n => n.videoId === video.id && n.type === 'process')
          .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())[0];

        if (existingNotification && existingNotification.status !== 'completed' && existingNotification.status !== 'failed') {
          // If there's an active analysis notification, restore the analysis state
          setAnalyzing(true);
          setProgress(existingNotification.progress || 0);
          setJobStatus(existingNotification.status === 'pending' ? 'PENDING' :
                      existingNotification.status === 'processing' ? 'PROCESSING' : null);
          setNotificationId(existingNotification.id);

          // Resume polling for job status
          pollJobStatus(video.id);
        }
      }
    }
  }, [video, isOpen, notifications]);

  const { data: allTags = [] } = useQuery(['tags'], () => getTags());

  const addTagMutation = useMutation(
    ({ videoId, tagId }: { videoId: number; tagId: number }) =>
      addTagToVideo(videoId, tagId),
    {
      onSuccess: () => {
        // Invalidate both the videos list and the specific video
        queryClient.invalidateQueries(['videos']);
        if (video) {
          queryClient.invalidateQueries(['videos', video.id]);
          // Force refetch the current video to update the UI
          queryClient.refetchQueries(['videos', video.id]);
        }
        toast.success('Tag added successfully');
      },
      onError: () => {
        toast.error('Failed to add tag');
      }
    }
  );

  const removeTagMutation = useMutation(
    ({ videoId, tagId }: { videoId: number; tagId: number }) =>
      removeTagFromVideo(videoId, tagId),
    {
      onSuccess: () => {
        // Invalidate both the videos list and the specific video
        queryClient.invalidateQueries(['videos']);
        if (video) {
          queryClient.invalidateQueries(['videos', video.id]);
          // Force refetch the current video to update the UI
          queryClient.refetchQueries(['videos', video.id]);
        }
        toast.success('Tag removed successfully');
      },
      onError: () => {
        toast.error('Failed to remove tag');
      }
    }
  );

  const createTagMutation = useMutation(
    (name: string) => createTag({ name, is_ai_generated: false }),
    {
      onSuccess: (newTag) => {
        // Invalidate the tags query to update the dropdown
        queryClient.invalidateQueries(['tags']);

        if (video) {
          // Add the new tag to the video
          addTagMutation.mutate({ videoId: video.id, tagId: newTag.id });

          // Invalidate and refetch the current video to update the UI
          queryClient.invalidateQueries(['videos', video.id]);
          queryClient.refetchQueries(['videos', video.id]);
        }

        setNewTagName('');
        toast.success('Tag created and added');
      },
      onError: () => {
        toast.error('Failed to create tag');
      }
    }
  );

  const reanalyzeVideoMutation = useMutation(
    (videoId: number) => triggerAnalysis(videoId),
    {
      onSuccess: () => {
        toast.success('Video analysis started', {
          id: 'analysis-started',
          duration: 3000,
          icon: '🔄'
        });
      },
      onError: () => {
        toast.error('Failed to queue video analysis', {
          duration: 4000
        });
      }
    }
  );

  // Function to get the latest analysis job for a video
  const getLatestAnalysisJobFromAPI = async (videoId: number) => {
    try {
      const response = await fetch(`/api/analysis-jobs/video/${videoId}`);
      const data = await response.json();
      return data && data.length > 0 ? data[0] : null;
    } catch (e) {
      console.error('Error fetching job status:', e);
      return null;
    }
  };

  // Function to poll the job status
  const pollJobStatus = async (videoId: number) => {
    // Constants for polling
    const LOG_INTERVAL = 10000; // 10 seconds between log messages
    const maxAttempts = 300; // Maximum number of polling attempts (5 minutes at 1s intervals)

    // Variables for tracking state
    let attempts = 0;
    let polling = true;
    let lastLogTime = Date.now();
    let lastStatus = null;
    let statusStartTime = Date.now();
    let startTime = Date.now();
    let errorCount = 0;
    let savedProgress = 0;

    // Storage key for saving progress
    const storageKey = `analysis_job_${videoId}`;

    // Try to restore state from localStorage
    let savedStateObj = null;
    try {
      const savedState = localStorage.getItem(storageKey);
      if (savedState) {
        savedStateObj = JSON.parse(savedState);
        // Only restore state if it's for the same video
        if (savedStateObj.videoId === videoId) {
          attempts = savedStateObj.attempts || 0;
          startTime = savedStateObj.startTime || Date.now();
          lastStatus = savedStateObj.lastStatus || null;
          statusStartTime = savedStateObj.statusStartTime || startTime;
          savedProgress = savedStateObj.progress || 0;

          // Update the UI with the saved progress immediately
          setProgress(savedProgress);
          // Only log important state restorations
          console.log(`Restored analysis job state for video ${videoId}, progress: ${Math.round(savedProgress * 100)}%`);
        }
      }
    } catch (e) {
      console.error('Error parsing saved analysis state:', e);
    }

    // Log start of polling only once
    console.log(`Starting to poll job status for video ${videoId}`);

    try {
      while (polling && attempts < maxAttempts) {
        attempts++;
        try {
          const job = await getLatestAnalysisJobFromAPI(videoId);

          // Only log status changes or at regular intervals to reduce console spam
          const currentTime = Date.now();
          const shouldLog = (job?.status !== lastStatus) ||
                          (currentTime - lastLogTime > LOG_INTERVAL);

          if (shouldLog) {
            console.log(`Poll attempt ${attempts}: Job status = ${job?.status || 'unknown'}`);
            lastLogTime = currentTime;
          }

          // Reset error counter on successful API call
          errorCount = 0;

          if (job) {
            // Check if the job has been running for too long (more than 30 minutes)
            const totalElapsedTime = Date.now() - startTime;

            // Auto-fail jobs that have been running for too long (30 minutes)
            if (totalElapsedTime > PROGRESS_THRESHOLDS.THIRTY_MIN) {
              console.log(`Job has been running for ${Math.round(totalElapsedTime/60000)} minutes, auto-failing due to timeout`);

              // Mark the job as failed
              setJobStatus('FAILED');
              setProgress(1.0); // Set progress to 100%

              // Clean up localStorage
              localStorage.removeItem(storageKey);

              // Update the notification
              if (notificationId) {
                updateNotification(notificationId, {
                  title: 'Video Analysis Timeout',
                  message: `Analysis timed out for: ${video?.title}. Please try again.`,
                  type: 'error',
                  progress: 1.0,
                  status: 'failed'
                });
              }

              // Show error toast
              toast.error('Analysis timed out. Please try again.', {
                id: 'analysis-timeout',
                duration: 4000,
                icon: '⏱️'
              });

              // Stop polling
              polling = false;
              break;
            }
          } else {
            // No job found, handle accordingly...
            console.log('No job found, continuing to poll');
          }
        } catch (e) {
          // Handle errors in the polling loop...
          errorCount++;
          console.error('Error polling job status:', e);
        }

        // Wait before the next poll
        const pollInterval = 1000; // 1 second
        await new Promise(res => setTimeout(res, pollInterval));
      }
    } catch (e) {
      console.error('Unhandled error in polling loop:', e);
    }

    // Handle max attempts reached...
    if (attempts >= maxAttempts) {
      console.log('Max polling attempts reached, stopping');
      setAnalyzing(false);
      setProgress(0);
      setJobStatus('FAILED');

      // Show timeout notification...
      toast.error('Analysis is taking too long. Please try again later.', {
        id: 'analysis-timeout',
        duration: 4000,
        icon: '⏱️'
      });
    }
  };

  const handleAddTag = (selected: any) => {
    if (!video) return;

    // Find tags that were added
    const currentTagIds = video.tags.map(tag => tag.id);

    const addedTags = selected.filter((tag: any) => !currentTagIds.includes(tag.id));

    // Add each new tag
    addedTags.forEach((tag: any) => {
      addTagMutation.mutate({ videoId: video.id, tagId: tag.id });
    });
  };

  const handleRemoveTag = (tagId: number) => {
    if (!video) return;
    removeTagMutation.mutate({ videoId: video.id, tagId });
  };

  const handleCreateTag = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!newTagName.trim() || !video) return;
    createTagMutation.mutate(newTagName.trim());
  };

  const handleReanalyze = async () => {
    // If already analyzing, show a notification and return
    if (analyzing) {
      toast('Video analysis is already in progress', {
        id: 'already-analyzing',
        duration: 3000,
        icon: '⏳'
      });
      return;
    }

    setAnalyzing(true);
    setProgress(0.05); // Start progress at 5%
    setJobStatus('PENDING');

    // Show a loading toast that will be updated when analysis completes
    toast.loading('Starting video analysis...', {
      id: 'analysis-progress',
      duration: 3000
    });

    // Create a notification for the analysis process with more detailed information
    const id = addNotification({
      title: 'Video Analysis',
      message: `Analyzing video: ${video!.title}`,
      type: 'process',
      progress: 0.05,
      status: 'pending',
      videoId: video!.id,
      actionText: 'View Video',
      onAction: () => {
        // This will open the video modal if it's closed
        if (!isOpen) {
          // You would need to implement this functionality in the parent component
          // For now, we'll just focus on the notification
        }
      }
    });

    setNotificationId(id);

    try {
      await reanalyzeVideoMutation.mutateAsync(video!.id);
      pollJobStatus(video!.id);
    } catch (e) {
      setAnalyzing(false);
      setProgress(0);
      setJobStatus(null);

      // Dismiss the loading toast and show an error
      toast.dismiss('analysis-progress');
      toast.error('Failed to start video analysis', {
        duration: 4000
      });

      // Update the notification to show the error
      updateNotification(id, {
        title: 'Video Analysis Failed',
        message: `Failed to analyze video: ${video!.title}`,
        type: 'error',
        progress: 1.0, // Set to 100% for failed jobs
        status: 'failed'
      });
    }
  };

  if (!video) return null;

  return (
    <Dialog
      open={isOpen}
      onClose={onClose}
      className="relative z-50"
    >
      <div className="fixed inset-0 bg-black/70" aria-hidden="true" />

      <div className="fixed inset-0 flex items-center justify-center p-4">
        <Dialog.Panel className="bg-gray-800 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden flex flex-col">
          <div className="flex justify-between items-center p-4 border-b border-gray-700">
            <Dialog.Title className="text-xl font-semibold text-white">
              {video.title}
            </Dialog.Title>
            <button
              id="close-modal-button"
              name="close-modal-button"
              onClick={onClose}
              aria-label="Close modal"
              className="text-gray-400 hover:text-white"
            >
              <XMarkIcon className="h-6 w-6" aria-hidden="true" />
            </button>
          </div>

          {/* Dialog content would go here */}

          {analyzing && (
            <div className="p-4 bg-gray-700 border-t border-gray-600 sticky bottom-0 left-0 right-0">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-white">Analysis Progress</span>
                <span className="text-sm text-gray-300">{Math.round(progress * 100)}%</span>
              </div>
              <div className="w-full bg-gray-600 rounded-full h-3">
                <div
                  className={`h-3 rounded-full transition-all duration-300 ${
                    jobStatus === 'FAILED' ? 'bg-red-600' :
                    jobStatus === 'COMPLETED' ? 'bg-green-500' : 'bg-blue-500'
                  }`}
                  style={{ width: `${progress * 100}%` }}
                ></div>
              </div>
              <div className="flex items-center justify-between mt-2">
                <div className="flex items-center">
                  <div className={`w-4 h-4 rounded-full mr-2 ${
                    jobStatus === 'PENDING' ? 'bg-yellow-500' :
                    jobStatus === 'PROCESSING' ? 'bg-blue-500 animate-pulse' :
                    jobStatus === 'COMPLETED' ? 'bg-green-500' : 'bg-red-500'
                  }`}></div>
                  <div className="text-sm text-gray-300">
                    {jobStatus === 'PENDING' && 'Video analysis queued...'}
                    {jobStatus === 'PROCESSING' && 'Analyzing video content...'}
                    {jobStatus === 'COMPLETED' && 'Analysis completed successfully!'}
                    {jobStatus === 'FAILED' && 'Analysis failed. Please try again.'}
                  </div>
                </div>

                {/* Add retry button for failed jobs */}
                {jobStatus === 'FAILED' && (
                  <button
                    onClick={handleReanalyze}
                    className="text-xs bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded"
                  >
                    Retry Analysis
                  </button>
                )}
              </div>
            </div>
          )}
        </Dialog.Panel>
      </div>
    </Dialog>
  );
};

export default VideoModal;