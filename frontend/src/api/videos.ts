import apiClient from './client';

export interface Video {
  id: number;
  title: string;
  file_path: string;
  duration?: number;
  width?: number;
  height?: number;
  tiktok_url?: string;
  tiktok_author?: string;
  download_date: string;
  transcript?: string;
  main_topic?: string;
  language?: string;  // Added language field
  created_at: string;
  updated_at: string;
  tags: Tag[];
}

export interface Tag {
  id: number;
  name: string;
  description?: string;
  is_ai_generated: boolean;
  created_at: string;
  updated_at: string;
}

export interface VideoFilter {
  tag_ids?: number[];
  start_date?: string;
  end_date?: string;
  search?: string;
  skip?: number;
  limit?: number;
}

export const getVideos = async (filters: VideoFilter = {}) => {
  const { data } = await apiClient.get<Video[]>('/videos/', { params: filters });
  return data;
};

export const getVideo = async (id: number) => {
  const { data } = await apiClient.get<Video>(`/videos/${id}`);
  return data;
};

export const triggerAnalysis = async (id: number) => {
  const { data } = await apiClient.post(`/videos/${id}/analyze`);
  return data;
};

export const addTagToVideo = async (videoId: number, tagId: number) => {
  // Use the correct API endpoint for adding tags to videos
  const { data } = await apiClient.post(`/video-tags/${videoId}/tags/${tagId}`);
  return data;
};

export const removeTagFromVideo = async (videoId: number, tagId: number) => {
  // Use the correct API endpoint for removing tags from videos
  const { data } = await apiClient.delete(`/video-tags/${videoId}/tags/${tagId}`);
  return data;
};

export const uploadVideo = async (file: File, title: string, analyze: boolean = true): Promise<Video> => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('title', title);
  formData.append('analyze', String(analyze)); // Convert boolean to string for form data

  const { data } = await apiClient.post<Video>('/videos/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return data;
};

export const deleteVideo = async (id: number) => {
  await apiClient.delete(`/videos/${id}`);
  // No data is typically returned on a successful DELETE, but adjust if your API differs
  return { ok: true };
};

export const checkStuckJobs = async () => {
  try {
    const { data } = await apiClient.post('/analysis-jobs/check-stuck-jobs');
    return data;
  } catch (error) {
    console.error('Error checking for stuck jobs:', error);
    throw error;
  }
};

// Cache for analysis jobs to reduce API calls
const analysisJobCache = new Map<number, { job: any, timestamp: number, status: string }>();

// In-flight requests tracking to prevent duplicate requests
const pendingRequests = new Map<number, Promise<any>>();

// Cache TTL (time to live) in milliseconds - varies by job status
const CACHE_TTL: Record<string, number> = {
  PENDING: 5000,     // 5 seconds for pending jobs
  PROCESSING: 3000,  // 3 seconds for processing jobs
  COMPLETED: 60000,  // 1 minute for completed jobs (essentially permanent)
  FAILED: 60000,     // 1 minute for failed jobs (essentially permanent)
  DEFAULT: 3000      // 3 seconds default
};

// Track last log time to reduce console spam
let lastJobLogTime = 0;
const JOB_LOG_INTERVAL = 5000; // Only log job-related messages every 5 seconds

export const getLatestAnalysisJob = async (videoId: number, bypassCache: boolean = false) => {
  try {
    const now = Date.now();
    const shouldLog = now - lastJobLogTime > JOB_LOG_INTERVAL;

    // Check if we have a request in progress for this video
    if (pendingRequests.has(videoId)) {
      if (shouldLog) {
        console.log(`Request already in progress for video ${videoId}, waiting for it to complete`);
        lastJobLogTime = now;
      }

      try {
        // Wait for the existing request to complete
        return await pendingRequests.get(videoId);
      } catch (error: any) {
        // Only log warnings for real errors, not cancellations
        if (error?.name !== 'CanceledError') {
          console.warn(`Pending request for video ${videoId} failed, will try again`);
        }
        // Continue with a new request if the pending one failed
        pendingRequests.delete(videoId);
      }
    }

    // Check if we have a cached job
    const cachedData = analysisJobCache.get(videoId);

    // Determine cache TTL based on job status
    let cacheTtl = CACHE_TTL.DEFAULT;
    if (cachedData?.job?.status) {
      cacheTtl = CACHE_TTL[cachedData.job.status] || CACHE_TTL.DEFAULT;
    }

    // Use cache if available, not bypassed, and not expired
    if (!bypassCache && cachedData && (now - cachedData.timestamp < cacheTtl)) {
      if (shouldLog) {
        console.log(`Using cached job data for video ${videoId}, age: ${Math.round((now - cachedData.timestamp)/1000)}s, status: ${cachedData.job?.status || 'unknown'}`);
        lastJobLogTime = now;
      }
      return cachedData.job;
    }

    // Create a new request promise
    const requestPromise = (async () => {
      try {
        // Add a cache-busting parameter if bypassCache is true
        const cacheParam = bypassCache ? `&nocache=${now}` : '';

        // Make the API request with a longer timeout for potentially slow responses
        const { data } = await apiClient.get(`/analysis-jobs/?video_id=${videoId}&limit=1${cacheParam}`, {
          timeout: 15000, // 15 seconds timeout
        });

        const job = data && data.length > 0 ? data[0] : null;

        // Cache the result with the current timestamp
        if (job) {
          analysisJobCache.set(videoId, {
            job,
            timestamp: now,
            status: job.status
          });

          // For completed or failed jobs, we can keep the cache longer
          if (job.status === 'COMPLETED' || job.status === 'FAILED') {
            if (shouldLog) {
              console.log(`Job for video ${videoId} is ${job.status}, caching for longer period`);
              lastJobLogTime = now;
            }
          }
        }

        return job;
      } finally {
        // Remove this request from pending requests when done
        pendingRequests.delete(videoId);
      }
    })();

    // Store the promise so we can reuse it for concurrent requests
    pendingRequests.set(videoId, requestPromise);

    // Wait for the request to complete
    return await requestPromise;
  } catch (error: any) {
    // Only log full errors for non-network issues to reduce console spam
    const isResourceError = error.message?.includes('ERR_INSUFFICIENT_RESOURCES') ||
                           error.code === 'ERR_NETWORK';

    // Check if we should log based on time since last log
    const currentTime = Date.now();
    const shouldLogNow = currentTime - lastJobLogTime > JOB_LOG_INTERVAL;

    if (isResourceError) {
      // For resource errors, just log a warning
      if (shouldLogNow) {
        console.warn(`Resource error when fetching job for video ${videoId}, using cached data if available`);
        lastJobLogTime = currentTime;
      }
    } else {
      // For other errors, log the full error
      console.error(`Error fetching analysis job for video ${videoId}:`, error);
    }

    // If we have a cached job, return it as a fallback even if it's old
    const cachedData = analysisJobCache.get(videoId);
    if (cachedData) {
      if (shouldLogNow) {
        console.log(`Returning cached job data as fallback for video ${videoId}, status: ${cachedData.job?.status || 'unknown'}`);
        lastJobLogTime = currentTime;
      }
      return cachedData.job;
    }

    // For resource errors, return a synthetic pending job instead of failing
    if (isResourceError) {
      if (shouldLogNow) {
        console.log(`Creating synthetic pending job for video ${videoId} due to resource error`);
        lastJobLogTime = currentTime;
      }
      const syntheticJob = {
        id: -1,
        video_id: videoId,
        status: 'PENDING',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        _isSynthetic: true
      };

      // Cache this synthetic job
      analysisJobCache.set(videoId, {
        job: syntheticJob,
        timestamp: currentTime - 4000, // Make it almost expired
        status: 'PENDING'
      });

      return syntheticJob;
    }

    // Re-throw the error if we don't have cached data and it's not a resource error
    throw error;
  }
};
