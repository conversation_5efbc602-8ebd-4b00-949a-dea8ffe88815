import axios from 'axios';
import { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';

// Request throttling mechanism
interface ThrottleState {
  lastRequestTime: number;
  pendingRequests: Map<string, { promise: Promise<AxiosResponse>, timestamp: number }>;
}

const throttle: ThrottleState = {
  lastRequestTime: 0,
  pendingRequests: new Map()
};

// Clean up old pending requests every 30 seconds to prevent memory leaks
setInterval(() => {
  const now = Date.now();
  let cleanupCount = 0;

  throttle.pendingRequests.forEach((entry, key) => {
    // Remove entries older than 30 seconds
    if (now - entry.timestamp > 30000) {
      throttle.pendingRequests.delete(key);
      cleanupCount++;
    }
  });

  // Only log if we actually cleaned something up
  if (cleanupCount > 0) {
    console.log(`Cleaned up ${cleanupCount} stale pending requests`);
  }
}, 30000);

// Minimum time between requests in milliseconds
const MIN_REQUEST_INTERVAL = 1000; // 1000ms (1 second) minimum between requests to reduce load

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: '/api',
  headers: {
    'Content-Type': 'application/json',
  },
  // Add a longer timeout to prevent quick failures
  timeout: 30000, // 30 seconds
});

// Add request interceptor for throttling
apiClient.interceptors.request.use(async (config) => {
  // Only throttle GET requests
  if (config.method?.toLowerCase() === 'get') {
    const url = config.url || '';
    const cacheKey = `${url}${JSON.stringify(config.params || {})}`;

    // Check if we already have a pending request for this URL
    if (throttle.pendingRequests.has(cacheKey)) {
      const pendingRequest = throttle.pendingRequests.get(cacheKey);

      // Only log for non-polling endpoints to reduce console spam
      if (!url.includes('analysis-jobs')) {
        console.log(`Request already in progress for ${url}, reusing promise`);
      }

      // @ts-ignore - We'll handle this in the response interceptor
      config._useExistingPromise = pendingRequest?.promise;
    }

    // Throttle requests to prevent overwhelming the server
    const now = Date.now();
    const timeSinceLastRequest = now - throttle.lastRequestTime;

    if (timeSinceLastRequest < MIN_REQUEST_INTERVAL) {
      const delayMs = MIN_REQUEST_INTERVAL - timeSinceLastRequest;

      // Only log throttling for non-polling endpoints to reduce console spam
      if (!url.includes('analysis-jobs')) {
        console.log(`Throttling request to ${url} for ${delayMs}ms`);
      }

      await new Promise(resolve => setTimeout(resolve, delayMs));
    }

    // Use a longer throttle interval for polling endpoints
    if (url.includes('analysis-jobs')) {
      // For polling endpoints, use a more aggressive throttling
      throttle.lastRequestTime = Date.now() + 500; // Add extra delay
    } else {
      throttle.lastRequestTime = Date.now();
    }
  }

  return config;
});

// Add response interceptor to handle retries and caching
apiClient.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const config = error.config as AxiosRequestConfig & {
      _retry?: boolean,
      _retryCount?: number,
      _useExistingPromise?: Promise<AxiosResponse>
    };

    // If we have a pending request for this URL, return that promise
    if (config._useExistingPromise) {
      return config._useExistingPromise;
    }

    // Check if this is a resource error
    const isResourceError = error.message?.includes('ERR_INSUFFICIENT_RESOURCES') ||
                           error.code === 'ERR_NETWORK';

    // Only retry GET requests to avoid side effects with mutations
    if (config && config.method === 'get') {
      // Initialize retry count if not set
      config._retryCount = config._retryCount || 0;

      // Maximum number of retries - use more retries for resource errors
      const maxRetries = isResourceError ? 5 : 3;

      // Check if we should retry the request
      if (config._retryCount < maxRetries) {
        config._retryCount += 1;

        // Implement exponential backoff - more aggressive for resource errors
        const baseDelay = isResourceError ? 3000 : 1000; // 3s base for resource errors
        const delayMs = baseDelay * Math.pow(2, config._retryCount); // 3s, 6s, 12s, 24s, 48s for resource errors

        // Wait for the delay
        await new Promise(resolve => setTimeout(resolve, delayMs));

        // Log retry attempt - but reduce logging for polling endpoints
        const url = config.url || '';
        const isPollingEndpoint = url.includes('analysis-jobs');

        if (isResourceError) {
          // Always log resource errors as warnings
          console.warn(`Resource error, retrying API request with longer delay (${config._retryCount}/${maxRetries}): ${url}`);
        } else if (!isPollingEndpoint || config._retryCount === 1) {
          // For non-polling endpoints, log all retries
          // For polling endpoints, only log the first retry to reduce console spam
          console.log(`Retrying API request (${config._retryCount}/${maxRetries}): ${url}`);
        }

        // Retry the request
        return apiClient(config);
      }
    }

    // If we've exhausted retries or it's not a GET request, reject with the error
    return Promise.reject(error);
  }
);

export default apiClient;
