import apiClient from './client';
import { Tag } from './videos';

export interface TagCreate {
  name: string;
  description?: string;
  is_ai_generated?: boolean;
}

export interface TagUpdate {
  name?: string;
  description?: string;
  is_ai_generated?: boolean;
}

export const getTags = async (isAiGenerated?: boolean) => {
  const params = isAiGenerated !== undefined ? { is_ai_generated: isAiGenerated } : {};
  const { data } = await apiClient.get<Tag[]>('/tags/', { params });
  return data;
};

export const getTag = async (id: number) => {
  const { data } = await apiClient.get<Tag>(`/tags/${id}`);
  return data;
};

export const createTag = async (tag: TagCreate) => {
  const { data } = await apiClient.post<Tag>('/tags/', tag);
  return data;
};

export const updateTag = async (id: number, tag: TagUpdate) => {
  const { data } = await apiClient.patch<Tag>(`/tags/${id}`, tag);
  return data;
};

export const deleteTag = async (id: number) => {
  const { data } = await apiClient.delete(`/tags/${id}`);
  return data;
};
