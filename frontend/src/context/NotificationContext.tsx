import React, { createContext, useContext, useState, useEffect } from 'react';

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'error' | 'warning' | 'process';
  timestamp: Date;
  read: boolean;
  progress?: number; // For process notifications
  status?: 'pending' | 'processing' | 'completed' | 'failed';
  videoId?: number; // For video-related notifications
  onAction?: () => void; // Optional action callback
  actionText?: string; // Optional action button text
}

// Helper function to serialize notifications for localStorage
const serializeNotifications = (notifications: Notification[]): string => {
  return JSON.stringify(notifications.map(notification => ({
    ...notification,
    timestamp: notification.timestamp.toISOString(),
    // Remove the callback function which can't be serialized
    onAction: undefined
  })));
};

// Helper function to deserialize notifications from localStorage
const deserializeNotifications = (serialized: string): Notification[] => {
  try {
    const parsed = JSON.parse(serialized);
    return parsed.map((notification: any) => ({
      ...notification,
      timestamp: new Date(notification.timestamp),
      // Restore the onAction function if needed based on the notification type
      onAction: notification.actionText ? () => {
        // This is a placeholder. The actual implementation will depend on your app's needs.
        console.log(`Action for notification ${notification.id} was triggered`);
      } : undefined
    }));
  } catch (e) {
    console.error('Error deserializing notifications:', e);
    return [];
  }
};

interface NotificationContextType {
  notifications: Notification[];
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => string;
  updateNotification: (id: string, updates: Partial<Notification>) => void;
  removeNotification: (id: string) => void;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  clearAllNotifications: () => void;
  unreadCount: number;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const NotificationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // Initialize notifications from localStorage if available
  const [notifications, setNotifications] = useState<Notification[]>(() => {
    const savedNotifications = localStorage.getItem('notifications');
    return savedNotifications ? deserializeNotifications(savedNotifications) : [];
  });

  const [unreadCount, setUnreadCount] = useState<number>(0);

  // Update unread count whenever notifications change
  useEffect(() => {
    const count = notifications.filter(notification => !notification.read).length;
    setUnreadCount(count);

    // Save notifications to localStorage whenever they change
    localStorage.setItem('notifications', serializeNotifications(notifications));
  }, [notifications]);

  // Add a new notification
  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const id = `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const newNotification: Notification = {
      ...notification,
      id,
      timestamp: new Date(),
      read: false,
    };

    setNotifications(prev => [newNotification, ...prev]);
    return id;
  };

  // Update an existing notification
  const updateNotification = (id: string, updates: Partial<Notification>) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id
          ? { ...notification, ...updates }
          : notification
      )
    );
  };

  // Remove a notification
  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(notification => notification.id !== id));
  };

  // Mark a notification as read
  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id
          ? { ...notification, read: true }
          : notification
      )
    );
  };

  // Mark all notifications as read
  const markAllAsRead = () => {
    setNotifications(prev =>
      prev.map(notification => ({ ...notification, read: true }))
    );
  };

  // Clear all notifications
  const clearAllNotifications = () => {
    setNotifications([]);
  };

  return (
    <NotificationContext.Provider
      value={{
        notifications,
        addNotification,
        updateNotification,
        removeNotification,
        markAsRead,
        markAllAsRead,
        clearAllNotifications,
        unreadCount,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};
