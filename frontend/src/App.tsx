import React, { useState, useRef } from 'react';
import { Tag, Video, VideoFilter } from './api/videos';
import Sidebar from './components/Sidebar';
import VideoGrid from './components/VideoGrid';
import VideoModal from './components/VideoModal';
import SearchBar from './components/SearchBar';
import NotificationBell from './components/NotificationBell';
import { uploadVideo } from './api/videos';

const App: React.FC = () => {
  // State for filters
  const [selectedTags, setSelectedTags] = useState<Tag[]>([]);
  const [startDate, setStartDate] = useState<string | null>(null);
  const [endDate, setEndDate] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');

  // State for video modal
  const [selectedVideo, setSelectedVideo] = useState<Video | null>(null);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);

  // State for upload
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState<boolean>(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Ref to trigger VideoGrid refresh (basic example)
  const [refreshKey, setRefreshKey] = useState<number>(0);

  // Combine all filters
  const filters: VideoFilter = {
    tag_ids: selectedTags.length > 0 ? selectedTags.map(tag => tag.id) : undefined,
    start_date: startDate || undefined,
    end_date: endDate || undefined,
    search: searchQuery || undefined,
    limit: 100,
  };

  const handleVideoClick = (video: Video) => {
    setSelectedVideo(video);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSearch = (query: string) => {
    setSearchQuery(query);
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setSelectedFile(event.target.files[0]);
      setUploadError(null);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      setUploadError("Please select a file first.");
      return;
    }

    setUploading(true);
    setUploadError(null);

    try {
      const uploadedVideo = await uploadVideo(selectedFile, selectedFile.name, true);
      console.log('Upload successful:', uploadedVideo);
      setSelectedFile(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
      setRefreshKey(prevKey => prevKey + 1);
    } catch (error) {
      console.error('Upload failed:', error);
      setUploadError(error instanceof Error ? error.message : "An unknown error occurred during upload.");
    } finally {
      setUploading(false);
    }
  };

  return (
      <div className="flex h-screen bg-gray-900 text-white">
        <Sidebar
          selectedTags={selectedTags}
          setSelectedTags={setSelectedTags}
          startDate={startDate}
          setStartDate={setStartDate}
          endDate={endDate}
          setEndDate={setEndDate}
        />

        <div className="flex-1 flex flex-col overflow-hidden">
          <header className="bg-gray-800 p-4 shadow-md space-y-4">
            <div className="flex justify-between items-center">
              <h1 className="text-2xl font-bold">TagTok</h1>
              <div className="flex items-center space-x-4 flex-grow max-w-xl ml-6">
                <div className="flex-grow">
                  <SearchBar onSearch={handleSearch} />
                </div>
                <NotificationBell />
              </div>
            </div>
          <div className="flex items-center space-x-2">
             <input
               type="file"
               ref={fileInputRef}
               onChange={handleFileChange}
               accept="video/*"
               className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-violet-50 file:text-violet-700 hover:file:bg-violet-100"
             />
             <button
               onClick={handleUpload}
               disabled={!selectedFile || uploading}
               className="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded disabled:opacity-50 disabled:cursor-not-allowed"
             >
               {uploading ? 'Uploading...' : 'Upload & Analyze'}
             </button>
             {uploadError && <span className="text-red-500 text-sm">{uploadError}</span>}
          </div>
        </header>

        <main className="flex-1 overflow-auto">
          <VideoGrid
            key={refreshKey}
            filters={filters}
            onVideoClick={handleVideoClick}
          />
        </main>
      </div>

      <VideoModal
        video={selectedVideo}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
      </div>
  );
};

export default App;
