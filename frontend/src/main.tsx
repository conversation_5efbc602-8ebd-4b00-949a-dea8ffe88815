import React from 'react'
import ReactD<PERSON> from 'react-dom/client'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { Toaster } from 'react-hot-toast'
import { NotificationProvider } from './context/NotificationContext'
import App from './App'
import './index.css'

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: 1,
    },
  },
})

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <NotificationProvider>
        <App />
        <Toaster
          position="top-right"
          containerStyle={{
            zIndex: 9999, // Ensure toasts appear above other elements
          }}
        />
      </NotificationProvider>
    </QueryClientProvider>
  </React.StrictMode>,
)
