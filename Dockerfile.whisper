FROM python:3.11-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    ffmpeg \
    curl \
    git \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies with optimized versions
RUN pip install --no-cache-dir \
    openai-whisper==20231117 \
    fastapi==0.109.2 \
    uvicorn==0.27.1 \
    python-multipart==0.0.9 \
    torch==2.1.2 \
    numpy==1.26.3 \
    pydantic==2.5.3 \
    redis==5.0.1 \
    ffmpeg-python==0.2.0 \
    numba==0.58.1 \
    tqdm==4.66.1

# Create directories for models and videos
RUN mkdir -p /models /videos /app/cache

# Set environment variables for better performance
ENV OMP_NUM_THREADS=8
ENV MKL_NUM_THREADS=8
ENV NUMEXPR_NUM_THREADS=8
ENV WHISPER_MODELS_DIR=/models
ENV WHISPER_CACHE_DIR=/app/cache
ENV PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512

# Copy the API server code
COPY whisper_server.py /app/
WORKDIR /app

# Expose port
EXPOSE 9000

# Start the API server with increased worker count and timeout
CMD ["uvicorn", "whisper_server:app", "--host", "0.0.0.0", "--port", "9000", "--workers", "4", "--timeout-keep-alive", "300"]