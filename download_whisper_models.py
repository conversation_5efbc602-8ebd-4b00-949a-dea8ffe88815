#!/usr/bin/env python3
"""
<PERSON>ript to download Whisper models and save them to a specified directory.
This allows pre-downloading models so they don't need to be downloaded at runtime.
"""

import os
import sys
import argparse
import logging
import whisper
import torch

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("whisper-model-downloader")

def download_model(model_name, output_dir):
    """Download a Whisper model and save it to the specified directory."""
    logger.info(f"Downloading Whisper model: {model_name}")
    
    # Check if CUDA is available
    device = "cuda" if torch.cuda.is_available() else "cpu"
    logger.info(f"Using device: {device}")
    
    try:
        # Load the model (this will download it if not already present)
        model = whisper.load_model(model_name, device=device)
        logger.info(f"Successfully downloaded model: {model_name}")
        
        # Get the model file path from the Whisper cache
        # The model should now be in the Whisper cache directory
        cache_dir = os.environ.get("WHISPER_CACHE_DIR", 
                                  os.path.join(os.path.expanduser("~"), ".cache", "whisper"))
        logger.info(f"Whisper cache directory: {cache_dir}")
        
        # List files in the cache directory
        if os.path.exists(cache_dir):
            logger.info(f"Files in cache directory: {os.listdir(cache_dir)}")
        else:
            logger.warning(f"Cache directory does not exist: {cache_dir}")
        
        return True
    except Exception as e:
        logger.error(f"Failed to download model {model_name}: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="Download Whisper models")
    parser.add_argument("--models", nargs="+", default=["base", "medium"], 
                        help="Models to download (default: base and medium)")
    parser.add_argument("--output-dir", default="/models", 
                        help="Directory to save models (default: /models)")
    
    args = parser.parse_args()
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Set environment variable for Whisper cache directory
    os.environ["WHISPER_MODELS_DIR"] = args.output_dir
    
    # Download each model
    success = True
    for model_name in args.models:
        if not download_model(model_name, args.output_dir):
            success = False
    
    if success:
        logger.info("All models downloaded successfully")
        return 0
    else:
        logger.error("Failed to download some models")
        return 1

if __name__ == "__main__":
    sys.exit(main())
