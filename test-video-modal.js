const { chromium } = require('playwright');

(async () => {
  // Launch the browser
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();

  // Navigate to the application
  await page.goto('http://localhost:3009');
  console.log('Navigated to the application');

  // Wait for the videos to load
  await page.waitForSelector('.grid > div:first-child', { timeout: 10000 });
  console.log('Videos loaded');

  // Click on the first video
  await page.click('.grid > div:first-child');
  console.log('Clicked on the first video');

  // Wait for the modal to open
  await page.waitForSelector('#headlessui-dialog-panel-\\:r1\\:');
  console.log('Modal opened');

  // Check if the modal has content
  const modalContent = await page.evaluate(() => {
    const modalPanel = document.querySelector('#headlessui-dialog-panel-\\:r1\\:');
    return modalPanel ? modalPanel.innerHTML : 'Modal panel not found';
  });

  console.log('Modal content:', modalContent.substring(0, 200) + '...');

  // Check if the video element exists
  const videoElement = await page.evaluate(() => {
    const video = document.querySelector('video');
    return video ? true : false;
  });

  console.log('Video element exists:', videoElement);

  // Close the modal
  await page.click('#close-modal-button');
  console.log('Closed the modal');

  // Close the browser
  await browser.close();
  console.log('Browser closed');
})();
